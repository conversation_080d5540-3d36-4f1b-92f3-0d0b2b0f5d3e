<?php

namespace App\Modules\CollectDebt\Ultilities;

use Carbon\Carbon;

class GenFeeUtil
{
	public static function getApplyDate(): Carbon
	{
		return Carbon::createFromDate(config('nextlend.NGAY_KHOI_TAO_LICH_SU_SINH_PHI'));
	}

	public static function getApplyDateAsString(string $format = 'd/m/Y'): string
	{
		return self::getApplyDate()->format($format);
	}

	public static function isApplyGenFee(Carbon $ngayBatDauHd): bool
	{
		return $ngayBatDauHd->gte(self::getApplyDate());
	}
} // End class
