<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction;

use Exception;
use Carbon\Carbon;
use App\Lib\Helper;
use GuzzleHttp\Pool;
use Illuminate\Support\Str;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPlan;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanSortableCollectionByRule;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\SetContractLevelByProfileSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CanCreateRequestIfHaveMultipleSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\Task\BuildParamTaoYeuCauTuDongTask;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\Task\ThucHienTaoLenhTrichTuDongTask;

class CreateRequestAutoBatchAction extends BatchProcessingAction
{
	public $timeout = 90;

	public function __construct()
	{
		ini_set('max_execution_time', $this->timeout);
	}

	public function run()
	{
		$canRunJob = parent::canRunJob();

		if (!$canRunJob) {
			return 'job will for nextday';
		}

		$results = CollectDebtPlan::query()
			->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
			->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->where('rundate', '<=', now()->format('Ymd'))
			->whereNotExists(function ($query) {
				$query->selectRaw("1")
							->from('debt_recovery_processing as p')
							->whereColumn('p.contract_code', 'debt_recovery_contract_plan.contract_code');
			})
			->limit(150)
			->select(['contract_code', 'profile_id'])
			->get();

		if ($results->isEmpty()) {
			return 'No plan for chunk';
		}

		$listContractCode = $results->unique('contract_code')->values()->toArray();

		$ranges = array_chunk($listContractCode, $this->batchSize);
		$processResult = $this->processBatch($ranges);
		return $processResult;
	}

	private function processBatch($ranges)
	{
		$client = parent::createHttpClient($this->timeout - 10);

		$baseUrl = config('app.url');

		$requests = function () use ($ranges, $baseUrl) {
			foreach ($ranges as $i => $batch) {
				$params = [];
				foreach ($batch as $item) {
					// tuỳ yêu cầu, có thể chỉ cần contract_code, hoặc cả contract_code,profile_id
					$params[] = $item['contract_code'] . ':' . $item['profile_id'];
				}
				$query = http_build_query([
					'batch' => $i + 1,
					'contracts' => implode(',', $params)
				]);

				$url = sprintf('%s/HandleCreateRequestAuto?%s', $baseUrl, $query);
				yield $batch => new Request('GET', $url);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => $this->batchCount,
			'fulfilled' => function ($response, $r) {
				// $body = (string)$response->getBody();
			},
			'rejected' => function (\Throwable $reason, $r) {
				$msg = "[CreateRequest --->range error: " . json_encode($r) . '] failed: ' . $reason->getMessage();
				Log::info($msg);
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);

		return ['msg' => 'all done'];
	}


	public function HandleCreateRequestAuto()
	{
		Log::info('HandleCreateRequestAuto', ['request' => request()->url()]);

		/**
		 * array:4 ["TEST-250314-USYBB:418", "TEST-250314-H16PF:418" ]
		 */
		$explodeContract = explode(',', request()->get('contracts'));

		foreach ($explodeContract as $item) {
			$contractCode = Str::before($item, ':');
			$profileId = Str::after($item, ':');

			$this->HandleRecord($contractCode, $profileId);
		}
	}

	public function HandleRecord(string $contractCode, $profileId)
	{
		$setContractLevel = app(SetContractLevelByProfileSubAction::class)->run($profileId, $contractCode);

		if (!$setContractLevel) {
			return $contractCode;
		}

		$listContractExclude = $this->getListHopDongCanNe();

		// 1. Đang bị exclude thì không cho tạo lệnh
		if (in_array($contractCode, $listContractExclude)) {
			return $contractCode;
		}

		// 2. Chỉ lấy ra các lịch có `is_process` là CHƯA XỬ LÝ
		$plan = CollectDebtSchedule::query()
			->where('contract_code', $contractCode)
			->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
			->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->where('rundate', '<=', now()->format('Ymd'))
			->orderByRaw('rundate ASC, cycle_number ASC, type ASC, is_settlement ASC')
			->first();

		if (!$plan) {
			// Không có plan thì kết thúc và giải phóng luồng luôn
			return $contractCode;
		}

		// 2. Update lịch vừa select lên thành đang xử lý ---> để thực hiện xử lý
		$wasUpdateProcessing = CollectDebtSchedule::query()
			->where('contract_code', $plan->contract_code)
			->where('id', $plan->id)
			->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
			->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->update([
				'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY
			]);

		if (!$wasUpdateProcessing) {
			$msg = "[ErrorUpdateProcessing---->$contractCode]" . 'Loi khong the update len thanh dang xu ly';
			Log::info($msg, ['planId' => $plan->id]);
			throw new Exception($msg);
		}


		// 3. 
		// Cập nhật các bản ghi lịch thu có cùng rundate về trạng thái ĐANG XỬ LÝ
		// Không thể biết có lịch quá khứ hay không, nên chỉ bắt catch
		try {
			$updatedPlanRows = CollectDebtSchedule::query()
				->where('contract_code', $plan->contract_code)
				->where('rundate', $plan->rundate)
				->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
				->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
				->where('id', '!=', $plan->id)
				->update([
					'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY
				]);
		} catch (\Throwable $th) {
			Log::info("[ErrorUpdateProcessing---->$contractCode]" . 'Loi update cac lich thu cung rundate', ['details' => Helper::traceError($th)]);

			// Doan nay phai update plan hien tai =>  ve da chua xu ly
			$updatedVeChuaXuLy = CollectDebtSchedule::query()->where('id', $plan->id)
				->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
				->update([
					'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY
				]);

			throw $th;
		}


		$listLichThuRefresh = CollectDebtSchedule::query()
			->join('debt_recovery_share', 'debt_recovery_share.contract_code', '=', 'debt_recovery_contract_plan.contract_code')
			->where('debt_recovery_contract_plan.contract_code', $plan->contract_code)
			->where('debt_recovery_contract_plan.rundate', $plan->rundate)
			->where('debt_recovery_contract_plan.is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
			->where('debt_recovery_contract_plan.status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->select([
				'debt_recovery_contract_plan.*',

				// Share
				'debt_recovery_share.partner_code',
				'debt_recovery_share.payment_guide'
			])
			->get();


		$isToanBoLichLaDangXuLy = $listLichThuRefresh->every(function (CollectDebtSchedule $p) {
			return $p->is_process == CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY;
		});

		// Toàn bộ các lịch phải là ĐANG XỬ LÝ --> thì mới có thể tạo yêu cầu được
		throw_if(!$isToanBoLichLaDangXuLy, new Exception('Toan bo lich chua ve dang xu ly'));

		$listLichThuTaoYeuCau = $listLichThuRefresh;

		$listLichThuTaoYeuCau = app(PlanSortableCollectionByRule::class)->sortCollection($listLichThuTaoYeuCau);

		$partnerCode = $listLichThuRefresh->first()->partner_code;

		// 5. Start transaction
		DB::beginTransaction();
		try {
			$buildParamTaoYeuCau = app(BuildParamTaoYeuCauTuDongTask::class)->run($listLichThuTaoYeuCau);
			$collectDebtRequest = app(ThucHienTaoLenhTrichTuDongTask::class)->run($buildParamTaoYeuCau, $listLichThuTaoYeuCau);

			$collectDebtProcessing = CollectDebtProcessing::query()->forceCreate([
				'contract_code' => $collectDebtRequest->contract_code,
				'partner_request_id' => $collectDebtRequest->partner_request_id,
				'expired_at' => Carbon::createFromTimestamp($collectDebtRequest->time_expired),
				'created_at' => now(),
				'updated_at' => now(),
				'checked_at' => now()->addMinutes(60),
				'push_time' => Helper::getSoLanVay($collectDebtRequest->contract_code, $partnerCode),
				'profile_id' => $profileId
			]);

			if (!$collectDebtProcessing) {
				throw new Exception('Loi khong tao duoc ban ghi processing...');
			}


			DB::commit();


			return $collectDebtRequest->partner_request_id;
		} catch (\Throwable $th) {
			DB::rollBack();
			Log::info("[ErrorTaoYeuCau----> $contractCode]", ['details' => Helper::traceError($th)]);

			// Update toàn bộ lịch thu cần tạo yc về is_process: CHƯA XỬ LÝ
			$updated = CollectDebtSchedule::query()
				->where('contract_code', $plan->contract_code)
				->whereIn('id', $listLichThuTaoYeuCau->pluck('id')->toArray())
				->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
				->update([
					'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY
				]);

			if (!$updated) {
				throw new Exception('Loi rollback trang thai, can sua tay');
			}
		}
	}

	public function getListHopDongCanNe(): array
	{
		$listHopDongDungJob = CollectDebtConfigAuto::getHopDongDungJobCache();
		return $listHopDongDungJob;
	}
} // End class