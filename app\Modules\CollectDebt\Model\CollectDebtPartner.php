<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\Traits\CollectDebtPartner\PartnerRequest;
use App\Modules\CollectDebt\Model\Traits\CollectDebtPartner\PartnerOtherData;
use App\Modules\CollectDebt\Model\Traits\CollectDebtPartner\PartnerStatusable;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;

class CollectDebtPartner extends Model
{
  use PartnerRequest,
    PartnerOtherData,
    PartnerStatusable;

  protected $table      = 'debt_recovery_partner';
  public    $timestamps = false;
  protected $appends    = [];
  protected $guarded    = [];

  /* -------------------- Relationship ----------------- */
  public function collectDebtRequest()
  {
    return $this->belongsTo(CollectDebtRequest::class, 'partner_request_id', 'partner_request_id');
  }

  public function collectDebtGuide()
  {
    return $this->belongsTo(CollectDebtGuide::class, 'payment_account_id', 'contract_code');
  }

  public function collectDebtSummary() {
    return $this->belongsTo(CollectDebtSummary::class, 'payment_account_id', 'contract_code');
  }

	public function partnerOnSummary() {
		return $this->belongsTo(CollectDebtSummary::class, 'contract_code', 'contract_code');
	}

  /* -------------------- Scope ----------------- */
  public function scopeWhereAvailableBalance($query)
  {
    return $query->whereRaw('amount_receiver - amount_payment - amount_refund > 0');
  }

  public function scopeWhereByContractCode($query, string $contractCode = '')
  {
		$contractCode = trim($contractCode);

		return $query->where(function ($q) use ($contractCode) {
			$q->where('payment_account_id', $contractCode)
				->orWhereJsonContains('other_data', [
					[
						'data' => [
							'contract_code' => $contractCode
						],
			
						'type' => 'CONTRACT'
					]
				]);
		});
  }

  /* -------------------- Event ----------------- */
  protected static function booted()
  {
    static::created(function ($collectDebtPartner) {
      if (!Str::contains($collectDebtPartner->created_by, 'username')) {
        $collectDebtPartner->created_by = StandardizedDataFilter::getUserAdminStructCompact([], ['username' => $collectDebtPartner->created_by]);

        $collectDebtPartner->save();
      }
    });
  }

  public function getCreatedByAttribute($value) {
    if (!Str::contains($value, 'username')) {
      return StandardizedDataFilter::getUserAdminStructCompact([], [
        'id' => 'cronjob',
        'username' => $value
      ]);
    }

    return $value;
  }

  /* -------------------- Methods ----------------- */
  // Số dư còn lại của công nợ (Số này để phân phối vào các Request kênh VA)
  public function getAmountBalance(): float
  {
    return $this->amount_receiver - $this->amount_payment - $this->amount_refund;
  }

  // Điều kiện có thể phân phối công nợ
  public function canPaidCollectRequest(): bool
  {
    return $this->getAmountBalance() > 0;
  }

  public function canCreateLedger(): bool
  {
    return $this->status == CollectDebtEnum::PARTNER_STT_DA_XU_LY;
  }
} // End class
