<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtReadyRecord;

class CreateReadyRecordSubAction
{
	public function run(string $partnerRequestId, string $contractCode)
	{
		$readyRecord = CollectDebtReadyRecord::query()->forceCreate([
			'partner_request_id' => $partnerRequestId,
			'contract_code' => $contractCode,
		]);

		if (!$readyRecord) {
			throw new Exception("PartnerRequestId: $partnerRequestId khong the tao ready record");
		}

		return $readyRecord;
	}
}
