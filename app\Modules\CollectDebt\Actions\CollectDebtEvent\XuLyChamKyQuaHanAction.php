<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtEvent;

use Exception;
use App\Lib\Helper;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtReminder;

class XuLyChamKyQuaHanAction
{
	public function run()
	{
		$fields = [
			'id', 
			'status_contract', 
			'is_send_mail_slow_cycle', 
			'is_send_mail_overdue', 
			'contract_code'
		];
		
		$q1 = CollectDebtSummary::query()
											->where('is_send_mail_slow_cycle', CollectDebtEnum::SUMMARY_CO_GUI_MAIL_CHAM_KY)
											->select($fields);

		$q2 = CollectDebtSummary::query()
											->where('is_send_mail_overdue', CollectDebtEnum::SUMMARY_CO_GUI_MAIL_QUA_HAN)
											->select($fields);
			
		$results = $q1->unionAll($q2)->limit(config('nextlend.BATCHING_LIMIT'))->get();
		
		if ($results->isEmpty()) {
			return 'No data for XuLyChamKyQuaHanAction';
		}

		foreach ($results as $collectDebtSummary) {
			try {
				DB::transaction(function () use ($collectDebtSummary) {
					$this->HandleOverCycleOverdue($collectDebtSummary);
				});
			}catch(\Throwable $th) {
				Log::info("[Error] XuLyChamKyQuaHanAction ---> $collectDebtSummary->contract_code: " . Helper::traceError($th));
			}
		}

		return ['msg' => 'done'];
	}

	private function markAsDone(CollectDebtSummary $collectDebtSummary)
	{
		$collectDebtSummary->is_send_mail_slow_cycle = 0;
		$collectDebtSummary->is_send_mail_overdue = 0;
		$collectDebtSummary->save();
		return 'Contract is done';
	}

	public function HandleOverCycleOverdue(CollectDebtSummary $collectDebtSummary)
	{
		if ($collectDebtSummary->is_send_mail_slow_cycle == 0 && $collectDebtSummary->is_send_mail_overdue == 0) {
			return "Contract: `$collectDebtSummary->contract_code` doesnt have any action";
		}

		if ($collectDebtSummary->isHopDongDaTatToan()) {
			return $this->markAsDone($collectDebtSummary);
		}


		// Cần tạo event mail quá hạn
		if ($collectDebtSummary->is_send_mail_overdue == CollectDebtEnum::SUMMARY_CO_GUI_MAIL_QUA_HAN) {
			$reminderInputs = [
				[
					'channel' => 'MAIL',
					'contract_code' => $collectDebtSummary->contract_code,
					'fired_at' => today()->setTime(11, 0, 0),
					'type' => 'QUA_HAN',
					'description' => 'quá hạn'
				]
			];

			$r = CollectDebtReminder::query()->insert($reminderInputs);
			if (!$r) {
				throw new Exception('Loi tao reminder qua han');
			}

			return $this->markAsDone($collectDebtSummary);
		}

		// Cần tạo event mail chậm kỳ
		if ($collectDebtSummary->is_send_mail_slow_cycle == CollectDebtEnum::SUMMARY_CO_GUI_MAIL_CHAM_KY) {
			$reminderInputs = [
				[
					'channel' => 'MAIL',
					'contract_code' => $collectDebtSummary->contract_code,
					'fired_at' => today()->setTime(11, 0, 0),
					'type' => 'CHAM_KY',
					'description' => 'chậm kỳ'
				]
			];

			$r =  CollectDebtReminder::query()->insert($reminderInputs);
			if (!$r) {
				throw new Exception('Loi tao reminder cham ky');
			}

			return $this->markAsDone($collectDebtSummary);
		}

		throw new Exception('Khong biet la loi gi va tao event nao');
	}
}  // End class