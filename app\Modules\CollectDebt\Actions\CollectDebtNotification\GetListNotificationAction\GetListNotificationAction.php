<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtNotification\GetListNotificationAction;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use App\Modules\CollectDebt\Model\CollectDebtNotification;
use App\Modules\CollectDebt\Resources\CollectDebtNotificationResourceCollection;

class GetListNotificationAction
{
	public function getCacheKey($page, $limit) {
		return 'CACHE_GET_LIST_NOTIFICATION_PAGE' . $page . '_LIMIT' . $limit;
	}

	public function run(Request $request)
	{
		$cacheKey = $this->getCacheKey($request->json('data.page', 1), $request->json('data.limit', 10));

		if (Cache::has($cacheKey)) {
			return Cache::get($cacheKey);
		}

		$listNotification = CollectDebtNotification::query()
			->orderBy('is_read', 'ASC')
			->orderBy('created_at', 'DESC')
			->paginate(
				$request->json('data.limit', 10),
				$request->json('data.fields', ['*']),
				'page',
				$request->json('data.page', 1)
			);


		$resource = new CollectDebtNotificationResourceCollection($listNotification);
		$response = $resource->toResponse($request)->getData(true);
		
		if (!empty($request->json('data.markAsRead'))) {
			$r = CollectDebtNotification::query()->where('is_read', 0)
																					 ->update(['is_read' => 1, 'read_at' => now()]);
		}

		$returnData = [
			'total_unread' => $this->getTotalUnread(),
			'data' => $response
		];

		Cache::put($cacheKey, $returnData, now()->addHours(1));

		return $returnData;
	}

	public function getTotalUnread(): int {
		return CollectDebtNotification::query()->where('is_read', 0)->count();
	}
} // End class
