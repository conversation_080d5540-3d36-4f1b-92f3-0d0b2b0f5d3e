<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtEvent;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Ultilities\ReminderUtil;
use Illuminate\Database\Eloquent\Collection;

class DongBoEventHopDongDangChayAction extends BatchProcessingAction
{
	public function run()
	{
		return CollectDebtGuide::query()
			->join('debt_recovery_summary', 'debt_recovery_contract_guide.contract_code', '=', 'debt_recovery_summary.contract_code')
			->where('debt_recovery_summary.status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN)
			->where('debt_recovery_summary.is_overdue', CollectDebtEnum::SUMMARY_KHONG_QUA_HAN)
			->select(['debt_recovery_contract_guide.*'])
			->chunk(20, function (Collection $listGuides) {
				foreach ($listGuides as $guide) {
					ReminderUtil::createDateSapDenHan($guide);
				}
			});
	}
}  // End class