<?php

namespace App\Lib;

use Illuminate\Support\Facades\Log;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use Elastic\Monolog\Formatter\ElasticCommonSchemaFormatter;

class Logs {

    public $_service;
    public $_nameFile;
    public $_data = [];

    public function __construct() {
        $this->_nameFile = 'nextlend_api_request_debt.log';
        $this->_service = env('APP_SERVICE_NAME', 'nextlend_api_request_debt');
    }

    public function writeFileLog($data) {
        if (empty($data) || (isset($data['message']) && $data['message'] == '[]')) {
					return true;
				}

				$message = $this->__buildMessageParams($data);

				if (empty($message) || $message == '[]') {
					return true; // stop hoàn toàn
				}

        if (env('INFRASTRUCTURE') == 'vps') {
            Log::channel($this->_service)->error($data);
        }

        $log = new Logger($this->_service);
        $handler = new StreamHandler('php://stdout', Logger::DEBUG);
        $handler->setFormatter(new ElasticCommonSchemaFormatter());
        $log->pushHandler($handler);
        $log->info($message, array('service.name' => $this->_service));
    }

    protected function __buildMessageParams($data) {
        $message = '';
        try {
            if (!isset($data) || empty($data)) {
                $message = null;
            } else if (isset($data) && !empty($data) && (is_array($data) || is_object($data))) {
                $message = json_encode($data, JSON_UNESCAPED_UNICODE);
            } else if (!empty($data) && is_string($data)) {
                $message = $data;
            } else {
                $message = 'Params not check';
            }
        } catch (\Exception $ex) {
            $message = 'Unknown error';
        }

        return $message;
    }

    public function concatData($data = []) {
        $this->_data[] = $data;
    }

    public function cc($data) {
        return $this->concatData($data);
    }

    public function logging() {
        $log_data = json_encode($this->_data, JSON_UNESCAPED_UNICODE|JSON_PRETTY_PRINT|JSON_UNESCAPED_SLASHES);
        return $this->writeFileLog($log_data);
    }
}
