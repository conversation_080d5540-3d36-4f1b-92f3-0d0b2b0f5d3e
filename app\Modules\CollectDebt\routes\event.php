<?php

use Illuminate\Support\Facades\Route; 
use App\Modules\CollectDebt\Actions\CollectDebtEvent\PushEventBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtEvent\GenEventContentAction;
use App\Modules\CollectDebt\Actions\CollectDebtEvent\XuLyChamKyQuaHanAction;
use App\Modules\EmailRemind\Controllers\DebtRecoveryContractEventController;
use App\Modules\CollectDebt\Actions\CollectDebtEvent\CreateEventChamKyQuaHanAction;
use App\Modules\CollectDebt\Actions\CollectDebtEvent\DongBoEventHopDongDangChayAction;

/*-------------------push event mail về hệ thống core--------------------*/
	Route::any('/PushEventBatchAction', function () {
		return app(PushEventBatchAction::class)->run();
	});

	Route::any('/ProcessEvent', function () {
		return app(PushEventBatchAction::class)->ProcessEvent();
	});
/*-------------------./End push event mail về hệ thống core--------------------*/

/*-------------------gen content cho event--------------------*/
	Route::any('/GenEventContentAction', function () {
		return app(GenEventContentAction::class)->run();
	});

	Route::any('/ProcessGenContent', function () {
		return app(GenEventContentAction::class)->ProcessGenContent();
	});
/*-------------------./End gen content cho event--------------------*/

/*-------------------xử lý summary chậm kỳ/quá hạn--------------------*/
	Route::any('/XuLyChamKyQuaHanAction', function () {
		return app(XuLyChamKyQuaHanAction::class)->run();
	});
/*-------------------./End xử lý summary chậm kỳ/quá hạn--------------------*/

/*-------------------Đồng bộ reminder cho HĐ đang chạy--------------------*/
	Route::any('/DongBoEventHopDongDangChayAction', function () {
		return app(DongBoEventHopDongDangChayAction::class)->run();
	});
/*-------------------./ Đồng bộ reminder cho HĐ đang chạy--------------------*/

Route::group([
	'middleware' => ['checktoken'],
], function () {
	Route::get('/GetTopEvent', DebtRecoveryContractEventController::class . '@GetTopEvent')->name('GetTopEvent');
});
