<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction;

use Exception;
use App\Lib\Helper;
use GuzzleHttp\Pool;
use App\Lib\TelegramAlert;
use Illuminate\Support\Str;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\XuLySoBaoMuonTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateAction\SubAction\GetLichThuByIdsSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\DigitalNotiTask\LuuLenhTrichCoTienDigitalNotiTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyReportTnexSubAction\SaveGiaoDichTnexSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\HachToanSoThuThieuSubAction\HachToanSoDoiLuongSubAction;

class AccountingLedgerBatchAction extends BatchProcessingAction
{
	public $timeout = 90;

	public function __construct()
	{
		ini_set('max_execution_time', $this->timeout);
	}

	public function run()
	{
		$results = CollectDebtLedger::query()
			->where('status', CollectDebtEnum::LEDGER_STT_CHUA_XU_LY)
			->limit(config('nextlend.BATCHING_LIMIT'))
			->pluck('id')
			->toArray();

		if (empty($results)) {
			return ['msg' => 'No data for AccountingLedgerBatchAction'];
		}

		$ranges = array_chunk($results, $this->batchSize);

		$this->processBatch($ranges);

		return ['msg' => 'ok done'];
	}

	private function processBatch($ranges)
	{
		$baseUrl = config('app.url');

		$requests = function () use ($ranges, $baseUrl) {
			foreach ($ranges as $r) {
				$url = sprintf('%s/ProcessAccoutingLedger?ids=%s', $baseUrl, implode(',', $r));
				yield $r => new Request('GET', $url, []);
			}
		};

		$client = $this->createHttpClient($this->timeout - 10);

		$pool = new Pool($client, $requests(), [
			'concurrency' => $this->batchCount,
			'fulfilled' => function ($response, $r) {
				// $body = (string)$response->getBody();
			},
			'rejected' => function (\Throwable $reason, $r) {
				$msg = "[AccoutingLedger ---> failed: " . $reason->getMessage();
				Log::info($msg);
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);

		return true;
	}

	public function ProcessAccoutingLedger() 
	{
		$ids = request()->get('ids');
		$listLedgerIds = array_map('intval', explode(',', $ids));

		$listLedger = CollectDebtLedger::query()
																	 ->whereIn('id', $listLedgerIds)
																	 ->where('status', CollectDebtEnum::LEDGER_STT_CHUA_XU_LY)
																	 ->get();
		if ($listLedger->isEmpty()) {
			return ['msg' => 'No data'];
		}

		foreach ($listLedger as $collectDebtLedger) {
			try {
				$this->HandleAccoutingLedger($collectDebtLedger);
			} catch (\Throwable $th) {
			
			}
		}
	}

	public function HandleAccoutingLedger(CollectDebtLedger $collectDebtLedgerCurrent)
	{
		if ($collectDebtLedgerCurrent->status != CollectDebtEnum::LEDGER_STT_CHUA_XU_LY) {
			return 'Ledger is processing...';
		}

		$logContext = ['LedgerId' => $collectDebtLedgerCurrent->id];

		$updatedLenDangXuLy = CollectDebtLedger::query()
			->where('id', $collectDebtLedgerCurrent->id)
			->where('status', CollectDebtEnum::LEDGER_STT_CHUA_XU_LY)
			->update([
				'status' => CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN
			]);
		if (!$updatedLenDangXuLy) {
			throw new Exception('khong the cap nhat so thang DANG HACH TOAN');
		}

		$collectDebtLedger = CollectDebtLedger::find($collectDebtLedgerCurrent->id); // khong dung refresh cho nay

		if ($collectDebtLedger->status != CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN) {
			throw new Exception('du lieu chua o trang thai dang hach toan, tu choi xu ly');
		}

		if ($collectDebtLedgerCurrent->amount != $collectDebtLedger->amount) {

			$updateVeChuaXuLy = CollectDebtLedger::query()
				->where(['id' => $collectDebtLedger->id, 'status' => CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN])
				->update(['status' => CollectDebtEnum::LEDGER_STT_CHUA_XU_LY]);

			$msg = '[LOI NGHIEM TRONG] - Amount ghi so dang bi sai thong tin';
			$logContext['ErrorAboutAmount'] = $msg;
			throw new Exception($msg);
		}


		DB::beginTransaction();
		try {
			// Xu ly neu la so bao muon
			if ($collectDebtLedger->isSoDoiTacBaoMuon() || $collectDebtLedger->isSoThuKhongCoLich()) {
				$logContext['SoBaoMuonHoacKhongCoLich'] = 'YES';
				$updatedSoBaoMuon = app(XuLySoBaoMuonTask::class)->run($collectDebtLedger);

				if (!$updatedSoBaoMuon) {
					$logContext['ErrUpdateSoBaoMuon'] = $updatedSoBaoMuon;
					throw new Exception('[LOI UPDATE SO BAO MUON]');
				}

				app(SaveGiaoDichTnexSubAction::class)->run($collectDebtLedger);
				DB::commit();
				return $collectDebtLedger;
			}

			$collectDebtLedger = $collectDebtLedger->load('collectDebtRequest');

			$collectDebtLedger->schedules = $this->getLichThuCuaSo($collectDebtLedger);


			//app(LuuLenhTrichCoTienDigitalNotiTask::class)->run($collectDebtLedger);

			$ledgerDaHachToan =  app(HachToanSoDoiLuongSubAction::class)->run($collectDebtLedger);
			app(SaveGiaoDichTnexSubAction::class)->run($collectDebtLedger);


			DB::commit();
			return $ledgerDaHachToan->id;
		} catch (\Throwable $th) {
			$err = Str::limit(Helper::traceError($th), 500);
			$msg = sprintf('env: %s ------ LedgerId: %s ------ Err: %s', config('app.env'), $id, $err);
			TelegramAlert::alertHachToan($msg);

			$logContext['ErrHachToan'] = $err;

			DB::rollBack();
			$updatedVeChuaXuLy = CollectDebtLedger::query()
				->where(['id' => $collectDebtLedger->id, 'status' => CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN])
				->update(['status' => CollectDebtEnum::LEDGER_STT_CHUA_XU_LY]);
			if (!$updatedVeChuaXuLy) {
				$logContext['ErrUpdateVeChuaXuLy'] = 'Yes';
				Log::info("[LoiHachToan ---->$id]", $logContext);
				throw new Exception('LOI ROLLBACK, khong the cap nhat ban ghi so ve CHUA XU LY');
			}

			Log::info("[LoiHachToan ---->$id]", $logContext);
			throw $th;
		}

		return $id;
	}

	public function getLichThuCuaSo(CollectDebtLedger $collectDebtLedger): Collection
	{
		if (empty($collectDebtLedger->plan_ids)) {
			return Collection::make();
		}

		$plans = app(GetLichThuByIdsSubAction::class)->run($collectDebtLedger->plan_ids);

		return $plans;
	}
}  // End class