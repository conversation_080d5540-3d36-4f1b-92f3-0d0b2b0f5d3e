<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtEvent;

use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\DebtRecoveryContractEventCreateSendAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\DebtRecoveryContractEventBuildContentImproveAction;

class PushEventBatchAction extends BatchProcessingAction
{
	public function run()
	{
		$results= CollectDebtContractEvent::query()
			->where('service_care_code', 'MAIL')
			->whereIn('status', [
				CollectDebtEnum::EVENT_STT_MOI_TAO, 
				CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL
			])
			->limit(config('nextlend.BATCHING_LIMIT'))
			->pluck('id')
			->toArray();

		if (empty($results)) {
			return ['msg' => 'No event data for PushEventBatchAction'];
		}

		$ranges = array_chunk($results, $this->batchSize);

		$this->processBatch($ranges);

		return ['msg' => 'ok done'];
	}

	private function processBatch($ranges)
	{
		$baseUrl = config('app.url');

		// Generator
		$requests = function () use ($ranges, $baseUrl) {
			foreach ($ranges as $r) {
				$url = sprintf('%s/ProcessEvent?ids=%s', $baseUrl, implode(',', $r));
				yield $r => new Request('GET', $url, []);
			}
		};

		$client = $this->createHttpClient();

		$pool = new Pool($client, $requests(), [
			'concurrency' => $this->batchCount,
			'fulfilled' => function ($response, $r) {
				// $body = (string)$response->getBody();
			},
			'rejected' => function (\Throwable $reason, $r) {
				$msg = "[ProcessEvent ---> failed: " . $reason->getMessage();
				Log::info($msg);
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);

		return true;
	}

	public function ProcessEvent() {
		$ids = request()->get('ids');
		$listEventIds = array_map('intval', explode(',', $ids));

		$events = CollectDebtContractEvent::query()
			->whereIn('id', $listEventIds)
			->whereIn('status', [
				CollectDebtEnum::EVENT_STT_MOI_TAO, 
				CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL
			])
			->get();

		if ($events->isEmpty()) {
			return 'No Data';
		}

		foreach ($events as $ev) {
			try {
				$this->HandleEvent($ev);
			}catch(\Throwable $th) {

			}
		}

		return true;
	}

	public function HandleEvent(CollectDebtContractEvent $collectDebtEvent)
	{
		if ($collectDebtEvent->status > CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL) {
			return "Event is processed";
		}

		if ($collectDebtEvent->status == CollectDebtEnum::EVENT_STT_MOI_TAO) {
			$content =  app(DebtRecoveryContractEventBuildContentImproveAction::class)->__buildMailContent($collectDebtEvent);
			$collectDebtEvent->content = $content;
			$collectDebtEvent->status = CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL;
			$collectDebtEvent->save();
		}

		// send mail
		return app(DebtRecoveryContractEventCreateSendAction::class)->__runMail($collectDebtEvent);
	}
}  // End class