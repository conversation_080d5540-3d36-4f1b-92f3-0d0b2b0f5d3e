<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtEvent\SubAction;

use Exception;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtReminder;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use App\Modules\CollectDebt\Actions\CollectDebtEvent\SubAction\ChamKyQuaHanTrait;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\DebtRecoveryContractEventBuildContentImproveAction;

class ChamKyChannelEmailSA
{
	use ChamKyQuaHanTrait;
	
	public function run(CollectDebtReminder $collectDebtReminder)
	{
		$collectDebtSummary = $collectDebtReminder->collectDebtGuide->collectDebtSummary;
		$collectDebtShare = $collectDebtReminder->collectDebtGuide->collectDebtShare;

		return DB::transaction(function () use ($collectDebtSummary, $collectDebtShare, $collectDebtReminder) {	
			$eventId = $this->HandleSlowCycleEvent($collectDebtSummary, $collectDebtShare);

			$collectDebtReminder->status_create_event = CollectDebtReminder::STT_DA_TAO_EVENT;
			$collectDebtReminder->create_event_at = now();
			$collectDebtReminder->description = 'Tạo event thành công';
			$collectDebtReminder->event_id = $eventId;
			$r = $collectDebtReminder->save();

			if (!$r) {
				throw new Exception('Loi cap nhat reminder');
			}
		});

		return $chamKy->contract_code;
	}


	public function HandleSlowCycleEvent(CollectDebtSummary $collectDebtSummary, CollectDebtShare $collectDebtShare)
	{
		$code = 'CONTRACT_OVERDUE_CYCLE';

		$inputs = [
			'category_care_code' => $code,
			'service_care_code' => 'MAIL',
			'data' => $this->FormatSharedData($collectDebtShare->toArray()),
			'description' => 'Tạo Event',
			'other_data' => [
				'summary' => $collectDebtSummary->toArray(),
			],
			'time_start' => time(),
			'contract_code' => $collectDebtSummary->contract_code,
		];

		$collectDebtEvent = $this->CreateEvent($inputs);

		if ( !$collectDebtEvent ) {
			$msg = "[ChamKy ----> Created event: $code failed ---> $collectDebtSummary->contract_code]";
			throw new Exception($msg);
		}

		$mailContent = app(DebtRecoveryContractEventBuildContentImproveAction::class)->__buildMailContent($collectDebtEvent);
		$collectDebtEvent->content = $mailContent;
		$collectDebtEvent->status = CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL;
		$collectDebtEvent->time_updated = time();
		
		if (!$collectDebtEvent->save()) {
			throw new Exception('Lỗi không update được nội dung mail');
		}
	}
} // End class
