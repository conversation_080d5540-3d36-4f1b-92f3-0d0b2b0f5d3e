<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\GenFeeReportAction;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGenFee;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtLateFee;
use App\Modules\CollectDebt\Ultilities\GenFeeUtil;

class GenFeeReportAction
{
	const FEE_CHAM_KY = 1;
	const FEE_QUA_HAN = 2;
	const FEE_PHAT_TRA_CHAM = 3;

	public function run()
	{
		return CollectDebtLedger::query()
		->where('status_gen_fee', CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT)
		->with('collectDebtSummary:contract_code,contract_data,status_contract')
		->select(['id', 'contract_code', 'other_data', 'status_gen_fee', 'time_accounting'])
		->chunkById(50, function (Collection $listLedger) {
			$this->handle($listLedger);
		});
	}

	public function handle(Collection $listLedger)
	{
		$inputs = [];
		$currentTimestamp = now()->timestamp;

		foreach ($listLedger as $collectDebtLedger) {
			$ledgerSummaryData = $collectDebtLedger->getOtherDataItem('SUMMARY');
			$collectDebtSummary = $collectDebtLedger->collectDebtSummary;
			
			if ( !GenFeeUtil::isApplyGenFee($collectDebtSummary->getTimeStartAsDate()) ) {
				$collectDebtLedger->status_gen_fee = CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT;
				$collectDebtLedger->save();
				continue;
			}

			if (empty($ledgerSummaryData['data']['fee_overdue']) && empty($ledgerSummaryData['data']['fee_overdue_cycle'])) {
				DB::transaction(function () use ($collectDebtLedger) {
					$collectDebtLedger->status_gen_fee = CollectDebtEnum::LEDGER_STT_ACTION_KHONG_XU_LY;
					$ledgerSaved = $collectDebtLedger->save();

					if (!$ledgerSaved) {
						throw new \Exception('Khong the cap nhat trang thai ledger khong xu ly');
					}
				});
				continue;
			}

			$items = [];

			// Xử lý phí quá hạn
			if (!empty($ledgerSummaryData['data']['fee_overdue'])) {
				$lateFeeProcessed = $this->processLateFees($collectDebtLedger, $items, $currentTimestamp);

				if ($lateFeeProcessed) {
					return true;
				}

				$items[] = $this->createFeeItem(
					$collectDebtLedger,
					self::FEE_QUA_HAN,
					$ledgerSummaryData['data']['fee_overdue'],
					$currentTimestamp
				);
			}

			// Xử lý phí chậm kỳ
			if (!empty($ledgerSummaryData['data']['fee_overdue_cycle'])) {
				$items[] = $this->createFeeItem(
					$collectDebtLedger,
					self::FEE_CHAM_KY,
					$ledgerSummaryData['data']['fee_overdue_cycle'],
					$currentTimestamp
				);
			}

			$inputs = array_merge($inputs, $items);
		}

		$this->insertGenFeesAndUpdateLedgers($inputs);

		return true;
	}

	private function processLateFees($collectDebtLedger, &$items, $currentTimestamp)
	{
		// SELECT không cần transaction
		$lateFees = CollectDebtLateFee::query()
			->where('contract_code', $collectDebtLedger->contract_code)
			->where('status', 1)
			->get();

		if ($lateFees->isEmpty()) {
			return false;
		}

		foreach ($lateFees as $lateFee) {
			if ($lateFee->type == CollectDebtEnum::TYPE_PHI_PHAT_TRA_CHAM) {
				$items[] = $this->createFeeItem(
					$collectDebtLedger,
					self::FEE_PHAT_TRA_CHAM,
					$lateFee->total_fee,
					$currentTimestamp
				);
			}

			if ($lateFee->type == CollectDebtEnum::TYPE_PHI_QUA_HAN_CHONG_PHI) {
				$items[] = $this->createFeeItem(
					$collectDebtLedger,
					self::FEE_QUA_HAN,
					$lateFee->total_fee,
					$currentTimestamp
				);
			}
		}

		// Chỉ wrap transaction cho các câu lệnh INSERT/UPDATE
		return DB::transaction(function () use ($items, $lateFees, $collectDebtLedger) {
			$insert = CollectDebtGenFee::query()->insertOrIgnore($items);

			if (!$insert) {
				throw new \Exception('Loi insert gen fee');
			}

			$lateFeeIds = $lateFees->pluck('id')->toArray();
			$updatedLateFees = CollectDebtLateFee::query()->whereIn('id', $lateFeeIds)->update(['status' => 3]);

			if ($updatedLateFees === 0) {
				throw new \Exception('Khong co late fee nao duoc cap nhat');
			}

			$collectDebtLedger->status_gen_fee = CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT;
			$ledgerSaved = $collectDebtLedger->save();

			if (!$ledgerSaved) {
				throw new \Exception('Khong the cap nhat trang thai ledger');
			}

			return true;
		});
	}

	private function createFeeItem($collectDebtLedger, $feeType, $feeAmount, $currentTimestamp)
	{
		return [
			'contract_code' => $collectDebtLedger->contract_code,
			'fee_type' => $feeType,
			'fee_amount' => $feeAmount,
			'time_gen_fee' => $collectDebtLedger->time_accounting,
			'time_created' => $currentTimestamp,
			'time_updated' => $currentTimestamp,
			'ledger_id' => $collectDebtLedger->id,
		];
	}

	private function insertGenFeesAndUpdateLedgers($inputs)
	{
		if (empty($inputs)) {
			return;
		}

		DB::transaction(function () use ($inputs) {
			$insert = CollectDebtGenFee::query()->insertOrIgnore($inputs);

			if (!$insert) {
				throw new \Exception('Loi insert gen fee');
			}

			$listLedgerIds = collect($inputs)->pluck('ledger_id')->unique()->toArray();

			$updatedLedgers = CollectDebtLedger::query()
				->whereIn('id', $listLedgerIds)
				->where('status_gen_fee', CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT)
				->update(['status_gen_fee' => CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT]);

			if ($updatedLedgers === 0) {
				throw new \Exception('Khong co ledger nao duoc cap nhat trang thai');
			}
		});
	}
}  // End class