<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtEvent\SubAction;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtReminder;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleFirstUpcomingPlanAction\SubAction\TaoMailContentSapDenKySubAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\GetTemplateMailSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleFirstUpcomingPlanAction\SubAction\ReplaceNoiDungMailSapToiKyAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleFirstUpcomingPlanAction\SubAction\CreateMailContentSapToiHanSubAction;

class SapDenKyChannelEmailSA
{
	public function run(CollectDebtReminder $collectDebtReminder)
	{
		$contractCode = $collectDebtReminder->contract_code;

		$plan = CollectDebtSchedule::query()->find($collectDebtReminder->plan_id);
		
		if ($plan->status == CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH) {
			
			$plan = CollectDebtSchedule::query()->where('contract_code', $contractCode)
																					->where('cycle_number', $plan->cycle_number)
																					->where('isfee', CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC)
																					->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
																					->where('is_settlement', 0)
																					->first();
					
			if (!$plan) {
				return $contractCode;
			}
		}

		$plan->load(['collectDebtShare', 'collectDebtSummary']);

		$careCode = $plan->time_start_as_date->isSameDay(now()) ? 'DEN_KY_THANH_TOAN' : 'NOTIFY_CONTRACT_DUE_PERIOD';

		$template = app(GetTemplateMailSubAction::class)->run([
			'customer_category_care_code' => $careCode,
			'customer_service_care_code' => 'MAIL',
		]);
		throw_if(empty($template['content']), new \Exception("Contract: `$contractCode` has empty template content"));

		$collectDebtEvent = app(TaoMailContentSapDenKySubAction::class)->run($plan, $careCode);
		throw_if(!$collectDebtEvent, new Exception('Loi khong tao duoc event'));

		$mailContent = app(ReplaceNoiDungMailSapToiKyAction::class)->run($plan, $collectDebtEvent, $template);
		throw_if(empty($mailContent), new Exception('Loi khong tao duoc noi dung mail'));

		$collectDebtEvent->status = CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL;
		$collectDebtEvent->content = $mailContent;
		$savedResult = $collectDebtEvent->save();

		throw_if(!$savedResult, new \Exception("Can not saved mail content for: EventId: $collectDebtEvent->id -- ContractCode: $contractCode"));

		$collectDebtReminder->status_create_event = CollectDebtReminder::STT_DA_TAO_EVENT;
		$collectDebtReminder->create_event_at = now();
		$collectDebtReminder->description = 'Tạo event thành công';
		$collectDebtReminder->event_id = $collectDebtEvent->id;
		$collectDebtReminder->save();

		return $contractCode;
	}
} // End class
