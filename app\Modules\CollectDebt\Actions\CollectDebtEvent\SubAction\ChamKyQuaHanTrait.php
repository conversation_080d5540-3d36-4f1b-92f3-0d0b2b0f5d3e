<?php
namespace App\Modules\CollectDebt\Actions\CollectDebtEvent\SubAction;

use App\Modules\EmailRemind\Model\CollectDebtContractEvent;

trait ChamKyQuaHanTrait
{
	public function CreateEvent($inputs)
	{
		$collectDebtEvent = CollectDebtContractEvent::query()->forceCreate([
			'category_care_code' => $inputs['category_care_code'],
			'service_care_code' => $inputs['service_care_code'],
			'data' => json_encode($inputs['data']),
			'description' => $inputs['description'],
			'other_data' => json_encode($inputs['other_data']),
			'time_start' => $inputs['time_start'],
			'contract_code' => $inputs['contract_code'],
			'time_created' => time(),
			'time_updated' => time(),
		]);

		if (!$collectDebtEvent) {
			throw new Exception("Loi tao event: " . $inputs['contract_code']);
		}

		return $collectDebtEvent;
	}

	public function FormatSharedData($value)
	{
		$data = [];

		if ($value) {
			if (isset($value['company_data']) && $value['company_data']) {
				$data['company'] = json_decode($value['company_data'], true);
			}
			if (isset($value['contract_data']) && $value['contract_data']) {
				$data['contract'] = json_decode($value['contract_data'], true);
			}
			if (isset($value['profile_data']) && $value['profile_data']) {
				$data['profile'] = json_decode($value['profile_data'], true);
			}
			if (isset($value['payment_guide']) && $value['payment_guide']) {
				$data['payment'] = json_decode($value['payment_guide'], true);
			}
			if (isset($value['list_fee']) && $value['list_fee']) {
				$data['list_fee'] = json_decode($value['list_fee'], true);
			}
		}
		return $data;
	}
}
