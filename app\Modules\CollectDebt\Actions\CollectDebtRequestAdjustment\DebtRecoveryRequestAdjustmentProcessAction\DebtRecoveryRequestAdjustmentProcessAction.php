<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction;

use DB;
use Exception;
use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\XuLyMoLaiLichSA;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyDieuChinhGhiSoSA\XuLyDieuChinhGhiSoSA;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyDieuChinhPartnerSA\XuLyDieuChinhPartnerSA;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyDieuChinhSummarySA\XuLyDieuChinhSummarySA;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyDieuChinhYeuCauTrichSA\XuLyDieuChinhYeuCauTrichSA;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task\HuyLenhTrichDangChayMLLTask;

class DebtRecoveryRequestAdjustmentProcessAction
{
  public function run()
  {
    $ra = CollectDebtRequestAdjustment::query()
																			->with('collectDebtSummary')
																			->where('status', CollectDebtEnum::REQUEST_ADJUSTMENT_STT_DUYET_2)
																			->first();
		if (!$ra) {
			return 'Empty';
		}

		// Cap nhat len thanh dang xu ly
		$updateLenDangXuLy = CollectDebtRequestAdjustment::query()
																										 ->where('id', $ra->id)
																										 ->where('status', CollectDebtEnum::REQUEST_ADJUSTMENT_STT_DUYET_2)
																										 ->update(['status' => CollectDebtEnum::REQUEST_ADJUSTMENT_STT_DANG_XU_LY]);
		if (!$updateLenDangXuLy) {
			mylog(['LOI CAP NHAT DANG XU LY' => $updateLenDangXuLy]);
			throw new Exception('LOI CAP NHAT DANG XU LY');
		}

		DB::beginTransaction();
    try {
			if ($ra->isSoTienTrichNhoHonSoTienTaoYcTrichNgay()) {
				mylog(['so tien trich thuc te < so tien tao trich ngay' => 'ok']);

				// Lam lai lich
        $xuLyMoLaiLich = app(XuLyMoLaiLichSA::class)->run($ra);
        
				// Doan nay kiem tra huy lenh trich dang chay neu nhu lich sinh ra cung rundate
				$huyLenhTrichDangChayResult = app(HuyLenhTrichDangChayMLLTask::class)->run($ra, $xuLyMoLaiLich['listLichThuDuocSinhRa']);

				// Cap nhat Dieu chinh partner
        $partner            = app(XuLyDieuChinhPartnerSA::class)->run($ra);

				// Cap nhat dieu chinh yeu cau trich
        $collectDebtRequest = app(XuLyDieuChinhYeuCauTrichSA::class)->run($ra);

				// Cap nhat dieu chinh lai so
        $ledger             = app(XuLyDieuChinhGhiSoSA::class)->run($ra, $collectDebtRequest, $xuLyMoLaiLich['meta']);

				// Tinh tien so vao tong hop
        $collectDebtSummary = app(XuLyDieuChinhSummarySA::class)->run($ra, $ledger);
      }

      $ra->time_completed = now()->timestamp;
      $ra->completed_by = Helper::getCronJobUser();
      $ra->status = CollectDebtEnum::REQUEST_ADJUSTMENT_STT_HOAN_THANH;
      $ra->other_data = $ra->putRAOtherData([
        'type' => 'RA_DONE',
        'note' => 'Hoàn thành yêu cầu điều chỉnh',
        'data' => $ra->makeHidden(['other_data', 'reference_data', 'collectDebtSummary']),
        'time_modified' => now()->timestamp
      ]);
      
			$result = $ra->save();

			if (!$result) {
				mylog(['Loi save ban ghi RA' => $result]);
				throw new Exception('Loi save ban ghi RA');
			}

			DB::commit();
      return $ra;
		} catch (\Throwable $th) {
			mylog(['Loi xu ly MLL' => Helper::traceError($th)]);
			DB::rollBack();

			$updateVeDuyet2 = CollectDebtRequestAdjustment::query()
																										->where('id', $ra->id)
																										->where('status', CollectDebtEnum::REQUEST_ADJUSTMENT_STT_DANG_XU_LY)
																										->update(['status' => CollectDebtEnum::REQUEST_ADJUSTMENT_STT_DUYET_2]);

			mylog(['Ket qua rollback: ' => $updateVeDuyet2]);																									

			throw $th;
		}
  } // End method
} // End class
