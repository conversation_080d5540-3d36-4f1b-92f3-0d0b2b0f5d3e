[2025-09-16 13:38:16] local.INFO: http://nextpay-web.local.com/api-request-debt/public/CreateRequestAutoBatchAction {"request":{"api_request_id":"DEBT_09161338_OMP0Se"}} 
[2025-09-16 13:43:58] local.INFO: http://nextpay-web.local.com/api-request-debt/public/CreateRequestAutoBatchAction {"request":{"api_request_id":"DEBT_09161343_H8RbME"}} 
[2025-09-16 13:43:59] local.INFO: http://nextpay-web.local.com/api-request-debt/public/HandleCreateRequestAuto?batch=1&contracts=TEST-OZVND2-L5%3A***************7 {"request":{"batch":"1","contracts":"TEST-OZVND2-L5:***************7","api_request_id":"DEBT_09161343_Tuyxkw"}} 
[2025-09-16 13:43:59] local.INFO: HandleCreateRequestAuto {"request":"http://nextpay-web.local.com/api-request-debt/public/HandleCreateRequestAuto"} 
[2025-09-16 13:43:59] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'lending_debt.debt_recovery_contract_level' doesn't exist (SQL: select * from `debt_recovery_contract_level` where (`profile_id` = ***************7 and `contract_code` = TEST-OZVND2-L5) limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'lending_debt.debt_recovery_contract_level' doesn't exist (SQL: select * from `debt_recovery_contract_level` where (`profile_id` = ***************7 and `contract_code` = TEST-OZVND2-L5) limit 1) at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:671)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(143): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(444): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtSchedule\\DebtRecoveryContractPlanCreateRequestAction\\SubAction\\SetContractLevelByProfileSubAction.php(60): Illuminate\\Database\\Eloquent\\Builder->firstOrCreate(Array, Array)
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtSchedule\\DebtRecoveryContractPlanCreateRequestAction\\CreateRequestAutoBatchAction.php(128): App\\Modules\\CollectDebt\\Actions\\CollectDebtSchedule\\DebtRecoveryContractPlanCreateRequestAction\\SubAction\\SetContractLevelByProfileSubAction->run('***************...', 'TEST-OZVND2-L5')
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtSchedule\\DebtRecoveryContractPlanCreateRequestAction\\CreateRequestAutoBatchAction.php(122): App\\Modules\\CollectDebt\\Actions\\CollectDebtSchedule\\DebtRecoveryContractPlanCreateRequestAction\\CreateRequestAutoBatchAction->HandleRecord('TEST-OZVND2-L5', '***************...')
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\cronjob.php(46): App\\Modules\\CollectDebt\\Actions\\CollectDebtSchedule\\DebtRecoveryContractPlanCreateRequestAction\\CreateRequestAutoBatchAction->HandleCreateRequestAuto()
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'lending_debt.debt_recovery_contract_level' doesn't exist at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:331)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(331): PDO->prepare('select * from `...')
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(664): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(143): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(444): Illuminate\\Database\\Eloquent\\Builder->first()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtSchedule\\DebtRecoveryContractPlanCreateRequestAction\\SubAction\\SetContractLevelByProfileSubAction.php(60): Illuminate\\Database\\Eloquent\\Builder->firstOrCreate(Array, Array)
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtSchedule\\DebtRecoveryContractPlanCreateRequestAction\\CreateRequestAutoBatchAction.php(128): App\\Modules\\CollectDebt\\Actions\\CollectDebtSchedule\\DebtRecoveryContractPlanCreateRequestAction\\SubAction\\SetContractLevelByProfileSubAction->run('***************...', 'TEST-OZVND2-L5')
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtSchedule\\DebtRecoveryContractPlanCreateRequestAction\\CreateRequestAutoBatchAction.php(122): App\\Modules\\CollectDebt\\Actions\\CollectDebtSchedule\\DebtRecoveryContractPlanCreateRequestAction\\CreateRequestAutoBatchAction->HandleRecord('TEST-OZVND2-L5', '***************...')
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\cronjob.php(46): App\\Modules\\CollectDebt\\Actions\\CollectDebtSchedule\\DebtRecoveryContractPlanCreateRequestAction\\CreateRequestAutoBatchAction->HandleCreateRequestAuto()
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#42 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#43 {main}
"} 
[2025-09-16 13:48:55] local.INFO: http://nextpay-web.local.com/api-request-debt/public/CreateRequestAutoBatchAction {"request":{"api_request_id":"DEBT_09161348_mcvysm"}} 
[2025-09-16 13:48:56] local.INFO: http://nextpay-web.local.com/api-request-debt/public/HandleCreateRequestAuto?batch=1&contracts=TEST-OZVND2-L5%3A***************7 {"request":{"batch":"1","contracts":"TEST-OZVND2-L5:***************7","api_request_id":"DEBT_09161348_2ghQG7"}} 
[2025-09-16 13:48:56] local.INFO: HandleCreateRequestAuto {"request":"http://nextpay-web.local.com/api-request-debt/public/HandleCreateRequestAuto"} 
[2025-09-16 14:26:09] local.INFO: AccountingLedgerBatchAction {"request":{"api_request_id":"DEBT_09161426_yduFvJ"}} 
[2025-09-16 14:28:58] local.INFO: AccountingLedgerBatchAction {"request":{"api_request_id":"DEBT_09161428_HOwrf3"}} 
[2025-09-16 14:28:58] local.INFO: ProcessAccoutingLedger {"request":{"ids":"32936,32945,34285,39842,42460,42461,42462","api_request_id":"DEBT_09161428_UifsrR"}} 
[2025-09-16 14:34:42] local.INFO: CreateRequestAutoBatchAction {"request":{"api_request_id":"DEBT_09161434_cTFTWB"}} 
[2025-09-16 14:34:43] local.INFO: HandleCreateRequestAuto {"request":{"batch":"1","contracts":"TEST-SVVQ6I-L1:1801","api_request_id":"DEBT_09161434_EznUbK"}} 
[2025-09-16 14:34:43] local.INFO: HandleCreateRequestAuto {"request":"http://nextpay-web.local.com/api-request-debt/public/HandleCreateRequestAuto"} 
[2025-09-16 14:35:31] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09161435_sqeWs2"}} 
[2025-09-16 14:35:31] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.id' in 'field list' (SQL: select `p`.`id`, `p`.`contract_code`, `debt_recovery_contract_level`.`level` from `debt_recovery_processing` as `p FORCE INDEX(``send_request_idx``)` inner join `debt_recovery_contract_level` on `debt_recovery_contract_level`.`contract_code` = `p`.`contract_code` where `is_sent_request` = 0 and `expired_at` >= 2025-09-16 14:35:31 and `push_time` <= 1758008131 and `debt_recovery_contract_level`.`is_send_cmd` = 0 order by `debt_recovery_contract_level`.`level` asc limit 120) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.id' in 'field list' (SQL: select `p`.`id`, `p`.`contract_code`, `debt_recovery_contract_level`.`level` from `debt_recovery_processing` as `p FORCE INDEX(``send_request_idx``)` inner join `debt_recovery_contract_level` on `debt_recovery_contract_level`.`contract_code` = `p`.`contract_code` where `is_sent_request` = 0 and `expired_at` >= 2025-09-16 14:35:31 and `push_time` <= 1758008131 and `debt_recovery_contract_level`.`is_send_cmd` = 0 order by `debt_recovery_contract_level`.`level` asc limit 120) at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:671)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select `p`.`id`...', Array, Object(Closure))
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select `p`.`id`...', Array, Object(Closure))
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select `p`.`id`...', Array, true)
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction.php(49): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\cronjob.php(58): App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction->run()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#37 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.id' in 'field list' at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:331)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(331): PDO->prepare('select `p`.`id`...')
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(664): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `p`.`id`...', Array)
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select `p`.`id`...', Array, Object(Closure))
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select `p`.`id`...', Array, Object(Closure))
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select `p`.`id`...', Array, true)
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction.php(49): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\cronjob.php(58): App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction->run()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
[2025-09-16 14:35:32] local.INFO: _ignition/health-check {"request":{"api_request_id":"DEBT_09161435_pb2yG3"}} 
[2025-09-16 14:36:46] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09161436_UI9kUF"}} 
[2025-09-16 14:36:46] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.id' in 'field list' (SQL: select p.id, p.contract_code, debt_recovery_contract_level.level from `debt_recovery_processing` as `p FORCE INDEX(``send_request_idx``)` inner join `debt_recovery_contract_level` on `debt_recovery_contract_level`.`contract_code` = `p`.`contract_code` where `is_sent_request` = 0 and `expired_at` >= 2025-09-16 14:36:46 and `push_time` <= ********** and `debt_recovery_contract_level`.`is_send_cmd` = 0 order by `debt_recovery_contract_level`.`level` asc limit 120) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.id' in 'field list' (SQL: select p.id, p.contract_code, debt_recovery_contract_level.level from `debt_recovery_processing` as `p FORCE INDEX(``send_request_idx``)` inner join `debt_recovery_contract_level` on `debt_recovery_contract_level`.`contract_code` = `p`.`contract_code` where `is_sent_request` = 0 and `expired_at` >= 2025-09-16 14:36:46 and `push_time` <= ********** and `debt_recovery_contract_level`.`is_send_cmd` = 0 order by `debt_recovery_contract_level`.`level` asc limit 120) at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:671)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select p.id, p....', Array, Object(Closure))
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select p.id, p....', Array, Object(Closure))
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select p.id, p....', Array, true)
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction.php(49): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\cronjob.php(58): App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction->run()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#37 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.id' in 'field list' at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:331)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(331): PDO->prepare('select p.id, p....')
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(664): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select p.id, p....', Array)
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select p.id, p....', Array, Object(Closure))
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select p.id, p....', Array, Object(Closure))
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select p.id, p....', Array, true)
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction.php(49): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\cronjob.php(58): App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction->run()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
[2025-09-16 14:36:47] local.INFO: _ignition/health-check {"request":{"api_request_id":"DEBT_09161436_28FB0y"}} 
[2025-09-16 14:37:54] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09161437_VkljOc"}} 
[2025-09-16 14:37:55] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'debt_recovery_processing.id' in 'field list' (SQL: select `debt_recovery_processing`.`id`, `debt_recovery_processing`.`contract_code`, `debt_recovery_contract_level`.`level` from `debt_recovery_processing` as `debt_recovery_processing FORCE INDEX(``send_request_idx``)` inner join `debt_recovery_contract_level` on `debt_recovery_contract_level`.`contract_code` = `debt_recovery_processing`.`contract_code` where `is_sent_request` = 0 and `expired_at` >= 2025-09-16 14:37:54 and `push_time` <= ********** and `debt_recovery_contract_level`.`is_send_cmd` = 0 order by `debt_recovery_contract_level`.`level` asc limit 120) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'debt_recovery_processing.id' in 'field list' (SQL: select `debt_recovery_processing`.`id`, `debt_recovery_processing`.`contract_code`, `debt_recovery_contract_level`.`level` from `debt_recovery_processing` as `debt_recovery_processing FORCE INDEX(``send_request_idx``)` inner join `debt_recovery_contract_level` on `debt_recovery_contract_level`.`contract_code` = `debt_recovery_processing`.`contract_code` where `is_sent_request` = 0 and `expired_at` >= 2025-09-16 14:37:54 and `push_time` <= ********** and `debt_recovery_contract_level`.`is_send_cmd` = 0 order by `debt_recovery_contract_level`.`level` asc limit 120) at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:671)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select `debt_re...', Array, Object(Closure))
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select `debt_re...', Array, Object(Closure))
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select `debt_re...', Array, true)
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction.php(53): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\cronjob.php(58): App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction->run()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#37 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'debt_recovery_processing.id' in 'field list' at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:331)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(331): PDO->prepare('select `debt_re...')
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(664): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `debt_re...', Array)
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select `debt_re...', Array, Object(Closure))
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select `debt_re...', Array, Object(Closure))
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select `debt_re...', Array, true)
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction.php(53): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\cronjob.php(58): App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction->run()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
[2025-09-16 14:37:55] local.INFO: _ignition/health-check {"request":{"api_request_id":"DEBT_09161437_c7lvqE"}} 
[2025-09-16 14:40:06] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09161440_WNDHnV"}} 
[2025-09-16 14:40:06] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'as debt_recovery_processing inner join `debt_recovery_contract_level` on `debt_r' at line 1 (SQL: select `debt_recovery_processing`.`id`, `debt_recovery_processing`.`contract_code`, `debt_recovery_contract_level`.`level` from debt_recovery_processing FORCE INDEX (send_request_idx) as debt_recovery_processing inner join `debt_recovery_contract_level` on `debt_recovery_contract_level`.`contract_code` = `debt_recovery_processing`.`contract_code` where `is_sent_request` = 0 and `expired_at` >= 2025-09-16 14:40:06 and `push_time` <= ********** and `debt_recovery_contract_level`.`is_send_cmd` = 0 order by `debt_recovery_contract_level`.`level` asc limit 120) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'as debt_recovery_processing inner join `debt_recovery_contract_level` on `debt_r' at line 1 (SQL: select `debt_recovery_processing`.`id`, `debt_recovery_processing`.`contract_code`, `debt_recovery_contract_level`.`level` from debt_recovery_processing FORCE INDEX (send_request_idx) as debt_recovery_processing inner join `debt_recovery_contract_level` on `debt_recovery_contract_level`.`contract_code` = `debt_recovery_processing`.`contract_code` where `is_sent_request` = 0 and `expired_at` >= 2025-09-16 14:40:06 and `push_time` <= ********** and `debt_recovery_contract_level`.`is_send_cmd` = 0 order by `debt_recovery_contract_level`.`level` asc limit 120) at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:671)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select `debt_re...', Array, Object(Closure))
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select `debt_re...', Array, Object(Closure))
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select `debt_re...', Array, true)
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction.php(53): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\cronjob.php(58): App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction->run()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#37 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'as debt_recovery_processing inner join `debt_recovery_contract_level` on `debt_r' at line 1 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:331)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(331): PDO->prepare('select `debt_re...')
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(664): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `debt_re...', Array)
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select `debt_re...', Array, Object(Closure))
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select `debt_re...', Array, Object(Closure))
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select `debt_re...', Array, true)
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction.php(53): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\cronjob.php(58): App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction->run()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
[2025-09-16 14:41:13] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09161441_U8XJAW"}} 
[2025-09-16 14:41:13] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'as p inner join `debt_recovery_contract_level` on `debt_recovery_contract_level`' at line 1 (SQL: select `p`.`id`, `p`.`contract_code`, `debt_recovery_contract_level`.`level` from debt_recovery_processing FORCE INDEX (send_request_idx) as p inner join `debt_recovery_contract_level` on `debt_recovery_contract_level`.`contract_code` = `p`.`contract_code` where `is_sent_request` = 0 and `expired_at` >= 2025-09-16 14:41:13 and `push_time` <= 1758008473 and `debt_recovery_contract_level`.`is_send_cmd` = 0 order by `debt_recovery_contract_level`.`level` asc limit 120) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'as p inner join `debt_recovery_contract_level` on `debt_recovery_contract_level`' at line 1 (SQL: select `p`.`id`, `p`.`contract_code`, `debt_recovery_contract_level`.`level` from debt_recovery_processing FORCE INDEX (send_request_idx) as p inner join `debt_recovery_contract_level` on `debt_recovery_contract_level`.`contract_code` = `p`.`contract_code` where `is_sent_request` = 0 and `expired_at` >= 2025-09-16 14:41:13 and `push_time` <= 1758008473 and `debt_recovery_contract_level`.`is_send_cmd` = 0 order by `debt_recovery_contract_level`.`level` asc limit 120) at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:671)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select `p`.`id`...', Array, Object(Closure))
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select `p`.`id`...', Array, Object(Closure))
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select `p`.`id`...', Array, true)
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction.php(53): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\cronjob.php(58): App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction->run()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#37 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'as p inner join `debt_recovery_contract_level` on `debt_recovery_contract_level`' at line 1 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:331)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(331): PDO->prepare('select `p`.`id`...')
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(664): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `p`.`id`...', Array)
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select `p`.`id`...', Array, Object(Closure))
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select `p`.`id`...', Array, Object(Closure))
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select `p`.`id`...', Array, true)
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction.php(53): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\cronjob.php(58): App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction->run()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
[2025-09-16 14:42:12] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09161442_BSA2Ya"}} 
[2025-09-16 14:42:13] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'as p inner join `debt_recovery_contract_level` as `cl` on `cl`.`contract_code` =' at line 1 (SQL: select `p`.`id`, `p`.`contract_code`, `cl`.`level` from debt_recovery_processing FORCE INDEX (send_request_idx) as p inner join `debt_recovery_contract_level` as `cl` on `cl`.`contract_code` = `p`.`contract_code` where `p`.`is_sent_request` = 0 and `p`.`expired_at` >= 2025-09-16 14:42:12 and `p`.`push_time` <= 1758008532 and `cl`.`is_send_cmd` = 0 order by `cl`.`level` asc limit 120) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'as p inner join `debt_recovery_contract_level` as `cl` on `cl`.`contract_code` =' at line 1 (SQL: select `p`.`id`, `p`.`contract_code`, `cl`.`level` from debt_recovery_processing FORCE INDEX (send_request_idx) as p inner join `debt_recovery_contract_level` as `cl` on `cl`.`contract_code` = `p`.`contract_code` where `p`.`is_sent_request` = 0 and `p`.`expired_at` >= 2025-09-16 14:42:12 and `p`.`push_time` <= 1758008532 and `cl`.`is_send_cmd` = 0 order by `cl`.`level` asc limit 120) at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:671)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select `p`.`id`...', Array, Object(Closure))
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select `p`.`id`...', Array, Object(Closure))
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select `p`.`id`...', Array, true)
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction.php(53): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\cronjob.php(58): App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction->run()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#37 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'as p inner join `debt_recovery_contract_level` as `cl` on `cl`.`contract_code` =' at line 1 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:331)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(331): PDO->prepare('select `p`.`id`...')
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(664): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `p`.`id`...', Array)
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select `p`.`id`...', Array, Object(Closure))
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select `p`.`id`...', Array, Object(Closure))
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select `p`.`id`...', Array, true)
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction.php(53): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\cronjob.php(58): App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestSendPaymentAction\\SendRequestBatchAction->run()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
[2025-09-16 14:43:04] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09161443_hrfwak"}} 
[2025-09-16 14:43:04] local.INFO: HandleSendRequestMpos {"request":{"ids":"3227","api_request_id":"DEBT_09161443_9iPj9G"}} 
[2025-09-16 14:43:05] local.INFO: partner-collect-debt-gateway/send-debt {"request":{"data":{"partner_request_id":"NL25091661674","partner_merchant_id":"22732","amount_payment":5360000,"contract_code":"TEST-SVVQ6I-L1","loan_original_amount":9200000,"deduction_per_day_amount":1000000,"loan_balance":10000000,"request_id":61674,"users_admin_id":"cronjob","time_begin":1758007980,"time_end":1758034800,"payment_channel":"MPOS"},"checksum":"437c1b985f12aaf15a3e2f6032a22a8d","channel_code":null,"time_request":1758008585,"version":"1.0","api_request_id":null}} 
[2025-09-16 14:43:06] local.INFO: [sendDebt] ---->NL25091661674 {"func":"sendDebt","inputRaw":{"nextlend_request_id":"NL25091661674","merchantId":"22732","lendingRequestId":"NL25091661674","debitAmount":5360000,"requestTime":"20250916144305","lendingId":"TEST-SVVQ6I-L1","loanOriginalAmount":9200000,"deductionPerDayAmount":1000000,"loanBalance":10000000,"partner_code":"MPOS","partnerCode":"NEXT_LEND","_prefix":"","recoverEndTime":"20250916220000999","recoverStartTime":"20250916143300000"},"requestInput":{"Fnc":"sendDebt","Checksum":"d0ffa093b3eb24e992e7d9565b5aa739","EncData":"sBNTQbQVimNsymNpiJzx7fqrF2OMH5jrXBrdiAe6bq1yD+OqiaLJJ97w99BNNyOo9zVSdLznayFnWVOaltY4j5IbQBQ6pNDbjyZqWXnlk8Q6yNR8qlsMSJ9NSSciCpc2nLYelFWsXqClTbKUr1SvgjPOsaknX9LCFT/6vG9pCRG7zmC7LAOXbS9tzu5CSWqkoYCCVnQmvFCK/OzGK/d3hi+vQzXSpUP/VEw9fi5soCtiiwesZ64tufUttcZRlQdrcUyg8oQI+/9KrafRyzW3+pta7Mxs8d3jA0K66LzuM52uQKPG8MTDUilRbNYHwLphpKu9V0AhatxCTaPijYZKEcMG9DakFsO6bynTjNave8IXD3q/UGTcNPbmnqjBtWbI0x7rJE3uQwA1gl5ViAgJhJ17zJUDRpdzl3HDRP89jWa3rrlSrqq4JiH9xY7ZvY6xWxPIAhInEbHH6YtP7k3lYBCPWkEquR4NpLTyjXz5fVluzJauepq/iN0Y+Ne5kbJMpClWJ7JDs5RvM2MvaldlzCW94XtUMFk+37r0A+7w3MU=","ChannelCode":"NEXTLENDV4","Version":"1.0"},"Result":{"Fnc":"sendDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25091661674\\\",\\\"mposDebtId\\\":141511805},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"25d5a39184c32cd6fce0872c09094b75","Description":"success"},"ParseError":{"errorCode":1000,"data":"{\"data\":{\"lendingRequestId\":\"NEXTLENDV4-NL25091661674\",\"mposDebtId\":141511805},\"status\":true,\"error\":{\"code\":1000,\"message\":\"DO_SERVICE_SUCCESS\",\"messageEn\":\"\"}}"}} 
[2025-09-16 15:17:39] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09161517_WCssDa"}} 
[2025-09-17 15:15:23] local.INFO: mock/CheckRedis {"request":{"api_request_id":"DEBT_09171515_3QikrY"}} 
[2025-09-17 15:15:33] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_09171515_D0uwqm"}} 
