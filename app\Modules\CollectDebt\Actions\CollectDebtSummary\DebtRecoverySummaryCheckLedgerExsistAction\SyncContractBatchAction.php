<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckLedgerExsistAction;

use Exception;
use GuzzleHttp\Pool;
use App\Lib\NextlendCore;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckLedgerExsistAction\DongBoVeHeThongHopDongAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\Task\GetHopDongNhanTienTuNguonThuThuaTask;

class SyncContractBatchAction extends BatchProcessingAction
{
	public NextlendCore $nextlendCore;

	public $timeout = 90;

	public function __construct(NextlendCore $nextlendCore)
	{
		ini_set('max_execution_time', $this->timeout);

		$this->nextlendCore = $nextlendCore;
	}

	public function run()
	{
		$listContractCode = CollectDebtSummary::query()
			->where('is_request_sync', CollectDebtEnum::SUMMARY_CO_DONG_BO)
			->limit(config('nextlend.BATCHING_LIMIT'))
			->pluck('contract_code')
			->toArray();

		if (empty($listContractCode)) {
			return ['msg' => 'No data contract need to sync'];
		}

		$ranges = array_chunk($listContractCode, $this->batchSize);

		$this->processBatch($ranges);

		return ['msg' => 'ok done'];
	}

	private function processBatch($ranges)
	{
		$client = parent::createHttpClient($this->timeout - 10);

		$baseUrl = config('app.url');

		$requests = function () use ($ranges, $baseUrl) {
			foreach ($ranges as $i => $r) {
				$url = sprintf('%s/SyncContract?contracts=%s', $baseUrl, implode(',', $r));
				yield $i => new Request('GET', $url);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => $this->batchCount,
			'fulfilled' => function ($response, $r) {
				// $body = (string)$response->getBody();
			},
			'rejected' => function (\Throwable $reason, $r) {
				$msg = "[SyncContract ---> failed: " . $reason->getMessage();
				Log::info($msg);
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);

		return ['msg' => 'all done'];
	}

	public function SyncContract()
	{
		$listContractCode = request()->get('contracts', '');
		if (empty($listContractCode)) {
			return ['msg' => 'No data contract need to sync'];
		}

		$listContractCode = explode(',', $listContractCode);

		$listContractCode = CollectDebtSummary::query()
			->whereIn('contract_code', $listContractCode)
			->get();

		if ($listContractCode->isEmpty()) {
			return ['msg' => 'No data contract need to sync'];
		}

		$listContractCode->map(function (CollectDebtSummary $collectDebtSummary) {
			try {
				$this->HandleSyncContract($collectDebtSummary);
			}catch(\Throwable $th) {
				
			}
		});
	}

	public function HandleSyncContract(CollectDebtSummary $collectDebtSummary)
	{
		if ($collectDebtSummary->isHopDongTest()) {
			return $this->__markAsSynced($collectDebtSummary->toArray());
		}

		$calcTime = app(DongBoVeHeThongHopDongAction::class)->__caculateTime($collectDebtSummary);
		$collectDebtSummary->number_day_status_debt = $calcTime['number_day_status_debt'];
		$collectDebtSummary->next_payment_period = $calcTime['next_payment_period'];

		$input = $collectDebtSummary->makeHidden(['other_data'])->toArray();

		// Xử lý dùng tiền thừa thanh toán cho HĐ khác
		$input['excess_handler'] = [
			'refund' => [
				'amount_refund' => $collectDebtSummary->total_amount_excess_refund
			],
			'repayment' => [
				'amount_repayment' => $collectDebtSummary->total_amount_repayment_debt,
				'list_contract' =>  []
			]
		];

		if (!empty($collectDebtSummary->total_amount_repayment_debt)) {
			$listHopDongNhanTien = app(GetHopDongNhanTienTuNguonThuThuaTask::class)->run($collectDebtSummary->contract_code);
			if (isset($listHopDongNhanTien['data']['listMaHopDongNhanTienCashIn'])) {
				$input['excess_handler']['repayment']['list_contract'] = $listHopDongNhanTien['data']['listMaHopDongNhanTienCashIn'];
			}
		}

		$nextlend = $this->nextlendCore->callRequest($input, 'ContractV4_summaryData', 'POST');
		$result = $nextlend->decryptData();
		if (empty($result['contract_id'])) {
			throw new Exception('Dong bo hop dong bi loi');
		}

		return $this->__markAsSynced($input);
	}

	private function __markAsSynced($input): string
	{
		$currntTimestamp = now()->timestamp;

		CollectDebtSummary::query()->where('contract_code', $input['contract_code'])->update([
			'is_request_sync' => CollectDebtEnum::SUMMARY_KHONG_DONG_BO,
			'time_updated' => now()->timestamp,
			'time_complated' => now()->timestamp
		]);

		//unset($input['other_data']);

		CollectDebtContractEvent::query()->updateOrCreate([
			'contract_code' => $input['contract_code'],
			'category_care_code' => 'CONTRACT',
			'service_care_code' => 'SYNC',
		], [
			'data' => json_encode([]),
			'description' => 'Đông bộ contract',
			'content' => '',
			'other_data' => json_encode([
				'summary' => $input
			]),
			'status' => 5,
			'time_start' => $currntTimestamp,
			'number' => 1,
			'time_expired' => $currntTimestamp,
			'time_sented' => $currntTimestamp,
			'time_created_content' => $currntTimestamp,
			'time_updated' => $currntTimestamp,
			'time_created' => $currntTimestamp,
		]);

		return $input['contract_code'];
	}
} // End class
