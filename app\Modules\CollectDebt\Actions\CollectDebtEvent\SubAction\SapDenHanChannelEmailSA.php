<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtEvent\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtReminder;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleSapToiHanAction\SubAction\ReplaceNoiDungBySummarySubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleSapToiHanAction\SubAction\TaoEventMailSapToiHanTatToanSubAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\GetTemplateMailSubAction;

class SapDenHanChannelEmailSA
{
	private function getTemplate(CollectDebtGuide $collectDebtGuide)
	{
		$templateTrichNgay = app(GetTemplateMailSubAction::class)->run([
			'customer_category_care_code' => $collectDebtGuide->time_end_as_date->isSameDay(now()) ? 'DEN_HAN_THANH_TOAN_HD_NGAY'
																																														 : 'NOTIFY_CONTRACT_DUE',
			'customer_service_care_code' => 'MAIL',
		]);

		$templateTrichKy = app(GetTemplateMailSubAction::class)->run([
			'customer_category_care_code' => $collectDebtGuide->time_end_as_date->isSameDay(now()) ? 'DEN_HAN_THANH_TOAN_HD_KY'
																																														 : 'NOTIFY_CONTRACT_DUE_PERIOD',

			'customer_service_care_code' => 'MAIL',
		]);

		return [
			'templateTrichNgay' => $templateTrichNgay,
			'templateTrichKy' => $templateTrichKy,
		];
	}

	public function run(CollectDebtReminder $collectDebtReminder, CollectDebtGuide $collectDebtGuide)
	{
		$templates = $this->getTemplate($collectDebtGuide);
		$templateTrichNgay = $templates['templateTrichNgay'];
		$templateTrichKy = $templates['templateTrichKy'];

		$contractCode = $collectDebtGuide->contract_code;

		
		$collectDebtEvent = app(TaoEventMailSapToiHanTatToanSubAction::class)->createOnly(
			$collectDebtGuide->collectDebtShare,
			$collectDebtGuide->collectDebtSummary,
			'NOTIFY_CONTRACT_DUE',
			$collectDebtReminder->channel
		);

		// Replace bien vao trong noi dung email
		$emailContent = '';

		if ($collectDebtGuide->isChiDanHopDongTrichNgay()) {
			throw_if(empty($templateTrichNgay['content']), new \Exception("Contract: `$contractCode` has empty template content"));

			$emailContent = app(ReplaceNoiDungBySummarySubAction::class)->run(
				$collectDebtEvent,
				$collectDebtGuide->collectDebtSummary,
				$templateTrichNgay
			);
		}

		if ($collectDebtGuide->isChiDanHopDongTrichKy()) {
			throw_if(empty($templateTrichKy['content']), new \Exception("Contract: `$contractCode` has empty template content"));

			$emailContent = app(ReplaceNoiDungBySummarySubAction::class)->run(
				$collectDebtEvent,
				$collectDebtGuide->collectDebtSummary,
				$templateTrichKy
			);
		}

		throw_if(empty($emailContent), new \Exception("Contract: `$contractCode` has empty email content"));

		$collectDebtEvent->status = CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL;
		$collectDebtEvent->content = $emailContent;
		$savedResult = $collectDebtEvent->save();

		throw_if(!$savedResult, new \Exception("Can not saved mail content for: EventId: $collectDebtEvent->id -- ContractCode: $contractCode"));

		// Tạo event mail thành công
		$collectDebtReminder->status_create_event = CollectDebtReminder::STT_DA_TAO_EVENT;
		$collectDebtReminder->create_event_at = now();
		$collectDebtReminder->description = 'Tạo event thành công';
		$collectDebtReminder->event_id = $collectDebtEvent->id;
		$collectDebtReminder->save();

		return $contractCode;
	}
} // End class
