<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction;

use Exception;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use App\Lib\TelegramAlert;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use App\Modules\CollectDebt\Ultilities\BatchUtil;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\SendRequestViaMposSubAction;

class SendRequestBatchAction extends BatchProcessingAction
{
	public $timeout = 90;

	public function __construct()
	{
		ini_set('max_execution_time', $this->timeout);
	}

	public function run()
	{
		$canRunJob = parent::canRunJob();

		if (!$canRunJob) {
			return 'job will for nextday';
		}

		$r = CollectDebtProcessing::query()
			->from('debt_recovery_processing as p FORCE INDEX(`send_request_idx`)')
			->where('is_sent_request', 0)
			->where('expired_at', '>=', now())
			->where('push_time', '<=', time())
			->limit(config('nextlend.BATCHING_LIMIT'))
			->pluck('id')
			->toArray();

		if (empty($r)) {
			return ['msg' => 'No data for SendRequestBatchAction'];
		}

		$ranges = array_chunk($r, $this->batchSize);

		$this->processBatch($ranges);
		return ['msg' => 'ok done'];
	}

	private function processBatch(array $ranges)
	{
		$client = parent::createHttpClient($this->timeout-10);

		$baseUrl = config('app.url');

		// Generator
		$requests = function () use ($ranges, $baseUrl) {
			foreach ($ranges as $r) {
				$url = sprintf('%s/HandleSendRequestMpos?ids=%s', $baseUrl, implode(',', $r));
				yield $r => new Request('GET', $url, []);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => $this->batchCount,
			'fulfilled' => function ($response, $r) {},
			'rejected' => function (\Throwable $reason, $r) {
				$msg = "[SendRequest --->range error: " . json_encode($r);
				Log::info($msg);
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);

		return ['msg' => 'all done'];
	}

	public function HandleSendRequestMpos()
	{
		$ids = request()->get('ids');
		$listIds = array_map('intval', explode(',', $ids));

		$listProcessing = CollectDebtProcessing::query()->with('collectDebtRequest')->whereIn('id', $listIds)->get();

		if ($listProcessing->isEmpty()) {
			return 'No data';
		}

		foreach ($listProcessing as $p) {
			try {
				$collectDebtRequest = $p->collectDebtRequest;
				$this->handle($collectDebtRequest);
			}catch(\Throwable $th) {
				$msg = "[SendRequest --->$p->partner_request_id] failed: " . $th->getMessage();
				Log::info($msg);
			}
			
		}

		return "ok";
	}

	public function handle(CollectDebtRequest $collectDebtRequest)
	{
		$partnerRequestId = $collectDebtRequest->partner_request_id;

		if (!empty($collectDebtRequest->partner_transaction_id)) {
			$this->markAsSent($partnerRequestId);
			return $partnerRequestId;
		}

		if ($collectDebtRequest->isRequestDaGhiSo()) {
			$this->markAsSent($partnerRequestId);
			return $partnerRequestId;
		}

		if ($collectDebtRequest->time_expired < time()) {
			$this->markAsSent($partnerRequestId);
			return $partnerRequestId;
		}

		$rq = app(SendRequestViaMposSubAction::class)->run($collectDebtRequest);

		if (empty($rq->partner_transaction_id)) {
			$msg = sprintf(
				'env: %s ---- PartnerRequestId: %s ---- Contract: %s dang khong co chung tu', 
				config('app.env'), 
				$partnerRequestId, 
				$collectDebtRequest->contract_code
			);
			
			@TelegramAlert::alertGuiLenhTrich($msg);
			throw new Exception($msg);
		}

		$this->markAsSent($partnerRequestId);

		return $partnerRequestId;
	}

	private function markAsSent(string $partnerRequestId)
	{
		$r = CollectDebtProcessing::query()
			->where('partner_request_id', $partnerRequestId)
			->update(['is_sent_request' => 1, 'updated_at' => now()]);

		return $r;
	}
}  // End class