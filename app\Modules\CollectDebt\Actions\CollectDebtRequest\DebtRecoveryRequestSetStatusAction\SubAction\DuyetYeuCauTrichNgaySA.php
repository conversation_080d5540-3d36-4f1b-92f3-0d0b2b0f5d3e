<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusAction\SubAction;

use Exception;
use App\Lib\Helper;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\CreateReadyRecordSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\SendRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task\TaoCongNoKhiLaTrichNgayTask;

class DuyetYeuCauTrichNgaySA
{
	public function run(CollectDebtRequest $collectDebtRequest, $request): CollectDebtRequest
	{
		$rq = app(SendRequestViaMposSubAction::class)->run($collectDebtRequest);
		throw_if(!$rq->isSentPayment(), new Exception('Không thể gửi yêu cầu trích ngay qua đối tác MPOS'));

		return DB::transaction(function () use ($collectDebtRequest, $request, $rq) {

			$collectDebtRequest->status = CollectDebtEnum::REQUEST_STT_DA_DUYET;
			$collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA;
			$collectDebtRequest->completed_by = $request->json('data.user_request_id');
			$collectDebtRequest->time_approved = time();
			$collectDebtRequest->approved_by = $request->json('data.user_request_id');
			$collectDebtRequest->time_completed = time();

			$r = $collectDebtRequest->save();

			if (!$r) {
				throw new Exception("Lỗi cập nhật lệnh trích ngay $collectDebtRequest->partner_request_id là đã nhận kết quả");
			}

			$partner = app(TaoCongNoKhiLaTrichNgayTask::class)->run($collectDebtRequest);

			if (!$partner) {
				throw new Exception("Lỗi không tạo được partner cho lệnh trích ngay $collectDebtRequest->partner_request_id");
			}

			app(CreateReadyRecordSubAction::class)->run(
				$collectDebtRequest->partner_request_id,
				$collectDebtRequest->contract_code
			);

			// Cap nhat lich la dang xu ly
			try {
				$planIds = $collectDebtRequest->getPlanIds();

				$updatedIsProcesVeDangXuLy = CollectDebtSchedule::query()
					->where('contract_code', $collectDebtRequest->contract_code)
					->whereIn('id', $planIds)
					->where('status', '!=', CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH)
					->update([
						'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY
					]);
				if (!$updatedIsProcesVeDangXuLy) {
					// chi duoc ghi log, ko dc throw loi
					mylog([
						'[LOI]' => 'Cap nhat trang thai lich trich ngay ve DANG XU LY that bai',
						'chi tiet' => $updatedIsProcesVeDangXuLy
					]);
				}
			} catch (\Throwable $th) {
				mylog(['[Loi]' => Helper::traceError($th)]);
			}

			// Tao ban ghi log trich ngay de con lam recheck
			if (!empty($rq->partner_transaction_id)) {
				$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $collectDebtRequest->contract_code)->first(['id', 'contract_code']);

				$paramLog = [
					'service_code' => CollectDebtEnum::RL_DICH_VU_KIEM_TRA_TRICH_NGAY,
					'partner_transaction_id' => $rq->partner_transaction_id
				];

				$collectDebtLog = CollectDebtLog::query()->firstOrCreate($paramLog, [
					'reference_id' => optional($collectDebtSummary)->id,
					'order_code' => sprintf('DEBTNOW_%s', $collectDebtRequest->id),
					'time_created' => now()->timestamp,
					'created_by' => $request->json('data.user_request_id', Helper::getCronJobUser()),
					'time_expired' => $collectDebtRequest->time_expired,
				]);

				if (!$collectDebtLog) {
					throw new Exception("Lỗi không tạo được bản ghi log");
				}
			}

			$rq->__apiMessage = 'Đã duyệt yêu cầu TRÍCH NGAY';
			return $rq;
		});
	}
}
