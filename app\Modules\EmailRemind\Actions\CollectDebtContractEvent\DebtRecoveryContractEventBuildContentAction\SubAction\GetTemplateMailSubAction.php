<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction;

use App\Lib\ApiCall;
use App\Utils\CommonVar;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Modules\CollectDebt\Enums\CacheEnum;

class GetTemplateMailSubAction
{
	public function run($request)
	{
		$templates = Cache::remember(CacheEnum::LIST_TEMPLATE_MAIL_AND_NOTI, now()->addDays(7), function () {
			return DB::connection('lending_data')->table('customer_message_template')
				->where('customer_service_care_code', 'MAIL')
				->get()
				->map(fn($item) => (array) $item)
				->toArray();
		});

		$params = [
			'customer_category_care_code' => $request['customer_category_care_code'],
			'customer_service_care_code' => $request['customer_service_care_code'],
		];

		if (isset($templates) && !empty($templates)) {
			$result = collect($templates)->where('customer_category_care_code', $params['customer_category_care_code'])
				->where('customer_service_care_code', $params['customer_service_care_code'])
				->first();

			if (!empty($result)) {
				return $result;
			}
		}

		$payload = [
			'module' => CommonVar::API_CUSTOMER_MODULE,
			'path' => '/template/getByServiceAndCat',
			'params' => $params,
			'method' => 'GET'
		];

		$result = (new ApiCall())->callFunctionApi($payload, false);
		return $result;
	}
}
