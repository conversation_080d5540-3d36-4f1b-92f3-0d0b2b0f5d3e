<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction;

use DB;
use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\Task\ThucHienGuiMailChamKyTask;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\Task\ThucHienGuiMailNhacNoTask;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\Task\ThucHienGuiMailQuaHanTask;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\Task\ThucHienDayTongHopVer1Task;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\Task\GetThongTinNguoiNhanMailTask;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\Task\DanhDauEventDongBoCuVeTrangThaiCuoiTask;

class DebtRecoveryContractEventCreateSendAction
{
	private array $__eventIds = [];

	public array $__returnData = [];

	public function initEventCreateSend()
	{
		try {
			for ($i = 1; $i <= 40; $i++) {
				$result = $this->run();

				if ($result == 'EMPTY') {
					break;
				}

				$this->__returnData[] = $result;
			}
		} catch (\Throwable $th) {
			mylog(['Loi xu ly' => Helper::traceError($th)]);
			@TelegramAlert::sendMessage(Helper::traceError($th));
		} finally {

		}

		return $this->__returnData;
	}

	public function run()
	{
		$collectDebtContractEvent = CollectDebtContractEvent::query()
																												->where('status', CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL)
																												->where('id', '!=', 593012);

		if (!empty($this->__eventIds)) {
			$collectDebtContractEvent = $collectDebtContractEvent->whereNotIn('id', $this->__eventIds);
		}

		$collectDebtContractEvent = $collectDebtContractEvent->first();

		if (!$collectDebtContractEvent) {
			return 'EMPTY';
		}

		$this->__eventIds[] = $collectDebtContractEvent->id;

		mylog(['EVENT INFO' => @$collectDebtContractEvent->only(['id', 'category_care_code', 'service_care_code'])]);

		// Cap nhat len thanh dang gui 
		$updatedLenThanhDangGui = CollectDebtContractEvent::query()
			->where('id', $collectDebtContractEvent->id)
			->where('status', CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL)
			->update([
				'time_start' => now()->timestamp,
				'time_updated' => now()->timestamp,
				'status' => CollectDebtEnum::EVENT_STT_DANG_GUI
			]);

		if (!$updatedLenThanhDangGui) {
			mylog(['Loi update event thanh dang gui' => 'yes']);
			throw new Exception('Loi update event thanh dang gui');
		}

		$collectDebtContractEvent = CollectDebtContractEvent::query()->find($collectDebtContractEvent->id);


		$r = [];
		try {
			switch ($collectDebtContractEvent->service_care_code) {
				case 'MAIL':
					$r = $this->__runMail($collectDebtContractEvent);
					break;
	
				case 'SYNC':
					mylog(['Log xu ly dong bo' => $collectDebtContractEvent->contract_code]);
					$r = $this->__runSync($collectDebtContractEvent);
					break;
	
				default:
					$r = ['Unknow'];
					break;
			}


			return $r;
		}catch(\Throwable $th) {
			mylog(['Err' => Helper::traceError($th)]);

			$updateVeTrangThaiDaTao = CollectDebtContractEvent::query()
																												->where('status', CollectDebtEnum::EVENT_STT_DANG_GUI)
																												->where('id', $collectDebtContractEvent->id)
																												->update([
																													'status' => CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL,
																													'time_updated' => now()->timestamp
																												]);

			mylog(['Ket qua cap nhat ve DA TAO NOI DUNG' => $updateVeTrangThaiDaTao]);

			return $th->getMessage();
		}
	}

	public function __runMail(CollectDebtContractEvent $collectDebtContractEvent)
	{
		if ($collectDebtContractEvent->contract_code == 'MPOS-2503071524924-L6') {
			return $collectDebtContractEvent->id;
		}

		$listEmailCc = app(GetThongTinNguoiNhanMailTask::class)->run($collectDebtContractEvent->contract_code);
		$dataResponse = [];

		switch ($collectDebtContractEvent->category_care_code) {
			case 'DEN_KY_THANH_TOAN': // Đến kỳ thanh toán (HĐ kỳ)
				$dataResponse = app(ThucHienGuiMailNhacNoTask::class)->run($collectDebtContractEvent, $listEmailCc);
				break;

			case 'DEN_HAN_THANH_TOAN_HD_NGAY': // Đến hạn tất toán HĐ ngày
				$dataResponse = app(ThucHienGuiMailNhacNoTask::class)->run($collectDebtContractEvent, $listEmailCc);
				break;

			case 'DEN_HAN_THANH_TOAN_HD_KY': // Đến hạn tất toán HĐ kỳ
				$dataResponse = app(ThucHienGuiMailNhacNoTask::class)->run($collectDebtContractEvent, $listEmailCc);
				break;

			case 'NOTIFY_CONTRACT_DUE': // Mail sap toi han
				$dataResponse = app(ThucHienGuiMailNhacNoTask::class)->run($collectDebtContractEvent, $listEmailCc);
				break;

			case 'CONTRACT_OVERDUE_CYCLE': // Mail cham ky
				$dataResponse = app(ThucHienGuiMailChamKyTask::class)->run($collectDebtContractEvent, $listEmailCc);
				break;


			case 'NOTIFY_CONTRACT_DUE1': // Mail qua han cap 1
				$dataResponse = app(ThucHienGuiMailQuaHanTask::class)->run($collectDebtContractEvent, $listEmailCc, 1);
				break;


			case 'NOTIFY_CONTRACT_DUE2': 	// Mail qua han cap 2
				$dataResponse = app(ThucHienGuiMailQuaHanTask::class)->run($collectDebtContractEvent, $listEmailCc, 2);
				break;


			case 'NOTIFY_CONTRACT_DUE3': // Mail qua han cap 3
				$dataResponse = app(ThucHienGuiMailQuaHanTask::class)->run($collectDebtContractEvent, $listEmailCc, 3);
				break;

			default:
				mylog([
					'Khong biet la case gi' => 'Yes',
					'ContractCode' => $collectDebtContractEvent->contract_code
				]);

				$dataResponse = [];
				break;
		} // End swicth

		mylog(['DataResponse' => $dataResponse]);

		if (!empty($dataResponse)) {
			// Thanh cong
			$updated = $collectDebtContractEvent->update([
				'time_sented' => now()->timestamp,
				'description' => 'Da gui mail',
				'status' => CollectDebtEnum::EVENT_STT_DA_TAO_YC_THANH_CONG
			]);

			mylog(['[SUCCESS] - Ket qua cap nhat tao yc' => $updated]);

			return $collectDebtContractEvent->id;
		} 

		throw new Exception('[UNKNOW] - Khong the thuc hien gui mail');
	}

	protected function __runSync(CollectDebtContractEvent $collectDebtContractEvent)
	{
		$result = [];

		switch ($collectDebtContractEvent->category_care_code) {
			case 'CONTRACT':
				mylog([
					'thuc hien day tong gop ve ver 1' => 'ok',
					'contract_code' => $collectDebtContractEvent->contract_code
				]);
				
				$result = app(ThucHienDayTongHopVer1Task::class)->run($collectDebtContractEvent);
				
				if (!empty($result)) {
					$huyCacEventDongBoCu = app(DanhDauEventDongBoCuVeTrangThaiCuoiTask::class)->run(
						$collectDebtContractEvent->contract_code, 
						$collectDebtContractEvent->id
					);
				}

				break;

			default:
				mylog([
					'khong biet case dong bo nay la gi' => 'err',
					'contract_code' => $collectDebtContractEvent->contract_code
				]);
				$result = [];
				break;
		}

		mylog(['result' => $result]);
		
		// Day tong hop thanh cong
		if (!empty($result)) {
			// Thanh cong
			$updated = $collectDebtContractEvent->update([
				'time_sented' => now()->timestamp,
				'description' => 'Da dong bo',
				'status' => CollectDebtEnum::EVENT_STT_DA_TAO_YC_THANH_CONG
			]);

			mylog(['[SUCCESS] - Ket qua cap nhat tao yc' => $updated]);
			return $result;
		}

		throw new Exception('[LOI] - khong the day dong hop ve ver 1');
	}
}
