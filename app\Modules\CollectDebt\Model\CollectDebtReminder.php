<?php

namespace App\Modules\CollectDebt\Model;


use Illuminate\Database\Eloquent\Model;
use App\Modules\CollectDebt\Model\CollectDebtGuide;

class CollectDebtReminder extends Model
{
	protected $table   = 'debt_recovery_reminder';
	protected $guarded = [];

	const STT_CHUA_TAO_EVENT = 1;
	const STT_DANG_TAO_EVENT = 2;
	const STT_DA_TAO_EVENT = 3;

	public function collectDebtGuide() {
		return $this->hasOne(CollectDebtGuide::class, 'contract_code', 'contract_code');
	}

} // End class
