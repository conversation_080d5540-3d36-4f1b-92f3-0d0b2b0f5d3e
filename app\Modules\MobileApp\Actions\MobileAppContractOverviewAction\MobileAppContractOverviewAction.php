<?php

namespace App\Modules\MobileApp\Actions\MobileAppContractOverviewAction;

use Exception;
use Carbon\Carbon;
use App\Lib\Helper;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPlan;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class MobileAppContractOverviewAction
{
	public array $response = [];

	public function run(Request $request)
	{
		$listConatractCode = $request->json('data.listContractCode', []);
		$collectDebtSummaries = CollectDebtSummary::query()
			->whereIn('contract_code', $listConatractCode)
			->select([
				'contract_code',
				'contract_data',
				'other_data',
				'contract_amount',
				'fee_overdue',
				'fee_overdue_cycle',
				'total_amount_paid',
				'total_fee_paid',
				'fee_overdue_paid',
				'fee_overdue_cycle_paid',
				'fee_overdue_reduction',
				'fee_overdue_cycle_reduction',
				'status_contract'
			])
			->get();

		if ($collectDebtSummaries->isEmpty()) {
			throw new Exception('Lỗi không tìm thấy thông tin HĐ');
		}

		$collectDebtSummaries->each(function (CollectDebtSummary $collectDebtSummary) {
			$planOtherData = $collectDebtSummary->getSummaryOtherDataItem('PLAN');

			$nextLoan = $this->getNextLoanPayment($collectDebtSummary, $planOtherData,);

			$overviewData = [
				'contractCode' => $collectDebtSummary->contract_code,

				'firstLoanPaymentDate' => Carbon::createFromTimestamp($planOtherData['data'][0]['time_cycle'])->format('d/m/Y'),
				'firstLoanPaymentAmount' => $this->getSoTienPhaiTraTheoKyThu($planOtherData['data'][0]),

				'settlementLoanPaymentDate' => Carbon::createFromTimestamp(Arr::last($planOtherData['data'])['time_cycle'])->format('d/m/Y'),
				'settlementLoanPaymentAmount' => Helper::priceFormat($collectDebtSummary->getTongTienConPhaiTra()),

				'nextLoanPaymentDate' => $nextLoan['nextLoanPaymentDate'],
				'nextLoanPaymentAmount' => $nextLoan['nextLoanPaymentAmount'],
			];

			if ($collectDebtSummary->isHopDongDaTatToan() || $collectDebtSummary->isDaQuaHanHopDong() || $collectDebtSummary->isDenHanHopDong()) {
				$overviewData['nextLoanPaymentDate'] = $overviewData['settlementLoanPaymentDate'];
				$overviewData['nextLoanPaymentAmount'] = $overviewData['settlementLoanPaymentAmount'];
			}

			if (Carbon::createFromFormat('d/m/Y',$overviewData['firstLoanPaymentDate'])->isFuture()) {
				$overviewData['nextLoanPaymentDate'] = $overviewData['firstLoanPaymentDate'];
				$overviewData['nextLoanPaymentAmount'] = $overviewData['firstLoanPaymentAmount'];
			}

			$this->response[] = $overviewData;
		});

		return $this->response;
	}

	public function getNextLoanPayment(CollectDebtSummary $collectDebtSummary, $planOtherData)
	{
		if (
			$collectDebtSummary->isHopDongDaTatToan()
			|| $collectDebtSummary->isDaQuaHanHopDong()
			|| $collectDebtSummary->isDenHanHopDong()
		) {
			return $this->returnEmpty();
		}

		$plan = CollectDebtPlan::query()
			->where('contract_code', $collectDebtSummary->contract_code)
			->where(function ($q) {
				return $q->whereBetween('time_start', [now()->startOfDay()->timestamp, now()->endOfDay()->timestamp])
								 ->orWhere('rundate', intval(date('Ymd')));
			})
			->first([
				'id',
				'contract_code',
				'request_amount_debit',
				'success_amount_debit',
				'rundate',
				'cycle_number'
			]);

		if (!$plan) {
			$this->returnEmpty();
		}

		$nextPlans = CollectDebtPlan::query()
			->where('contract_code', $collectDebtSummary->contract_code)
			->whereRaw("(cycle_number < ? AND status IN (?, ?) OR cycle_number = ? )", [
				$plan->cycle_number+1,

				CollectDebtEnum::SCHEDULE_STT_MOI, 
				CollectDebtEnum::SCHEDULE_STT_DANG_XU_LY,

				$plan->cycle_number+1,
			])
			->get([
				'id',
				'contract_code',
				'request_amount_debit',
				'success_amount_debit',
				'time_start',
				'rundate',
				'cycle_number',
				'status'
			]);
		if ($nextPlans->isEmpty()) {
			return $this->returnEmpty();
		}

		$amount = 0;
		foreach ($nextPlans as $p) {
			if ($p->status == CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH) {
				$amount += 0;
			}else {
				$amount = $amount + ($p->request_amount_debit - $p->success_amount_debit);
			}
		}
		
		$maxCycle = $nextPlans->max('cycle_number');
		$maxItem = $nextPlans->where('cycle_number', $maxCycle)->first();

		return [
			'nextLoanPaymentDate' => Carbon::createFromTimestamp($maxItem->time_start)->format('d/m/Y'),
			'nextLoanPaymentAmount' => Helper::priceFormat($amount)
		];
	}

	public function returnEmpty()
	{
		return [
			'nextLoanPaymentDate' => '--',
			'nextLoanPaymentAmount' => '--'
		];
	}
	/**
	 * áp dụng được với ngày luôn
	 * [
      "id" => 74382
      "time_cycle" => 1746118800
      "time_over_cycle" => 0
      "amount" => 5000000
      "fee" => 0
      "amount_paid" => 5000000
      "fee_paid" => 0
      "overdue_cycle" => 2
      "note" => "Hợp đồng V4"
      "status" => 3
      "data" => array:4 [
        "id" => 74382
        "fee_overdue_cycle_reduction" => 0
        "fee_overdue" => 0
        "fee_overdue_cycle" => 0
      ]
    ]
	 */
	public function getSoTienPhaiTraTheoKyThu($summaryPlanItem = [])
	{
		$amount = ($summaryPlanItem['amount'] + $summaryPlanItem['fee']) - ($summaryPlanItem['amount_paid'] + $summaryPlanItem['fee_paid']);
		if ($amount <= 0) {
			$amount = 0;
		}
		return Helper::priceFormat($amount);
	}
}
