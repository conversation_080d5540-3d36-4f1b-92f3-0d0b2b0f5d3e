<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtEvent\SubAction;

use Exception;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtReminder;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use App\Modules\CollectDebt\Actions\CollectDebtEvent\SubAction\ChamKyQuaHanTrait;
use App\Modules\CollectDebt\Actions\CollectDebtEvent\SubAction\ChamKyChannelEmailSA;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\DebtRecoveryContractEventBuildContentImproveAction;

class QuaHanChannelEmailSA
{
	use ChamKyQuaHanTrait;
	
	public function run(CollectDebtReminder $collectDebtReminder)
	{
		$collectDebtSummary = $collectDebtReminder->collectDebtGuide->collectDebtSummary;
		$collectDebtShare = $collectDebtReminder->collectDebtGuide->collectDebtShare;

		return DB::transaction(function () use ($collectDebtSummary, $collectDebtShare, $collectDebtReminder) {	
			$eventId = $this->HandleOverdueEvent($collectDebtSummary, $collectDebtShare);

			$collectDebtReminder->status_create_event = CollectDebtReminder::STT_DA_TAO_EVENT;
			$collectDebtReminder->create_event_at = now();
			$collectDebtReminder->description = 'Tạo event thành công';
			$collectDebtReminder->event_id = $eventId;
			$r = $collectDebtReminder->save();

			if (!$r) {
				throw new Exception('Loi cap nhat reminder');
			}
		});

		return $chamKy->contract_code;
	}

	public function HandleOverdueEvent(CollectDebtSummary $collectDebtSummary, CollectDebtShare $collectDebtShare)
	{
		$days = $collectDebtSummary->number_day_overdue;
		$code = '';

		if ($days >= 0 && $days < 5) {
			$code = 'NOTIFY_CONTRACT_DUE1';
		} else if ($days >= 5 && $days <= 10) {
			$code = 'NOTIFY_CONTRACT_DUE2';
		} else if ($days >= 11) {
			$code = 'NOTIFY_CONTRACT_DUE3';
		}

		$inputs = [
			'category_care_code' => $code,
			'service_care_code' => 'MAIL',
			'data' => $this->FormatSharedData($collectDebtShare->toArray()),
			'description' => 'Tạo Event',
			'other_data' => [
				'summary' => $collectDebtSummary->toArray(),
			],
			'time_start' => time(),
			'contract_code' => $collectDebtSummary->contract_code,
		];

		$collectDebtEvent = $this->CreateEvent($inputs);

		if ( !$collectDebtEvent ) {
			$msg = "[QuaHan ----> Created event: $code failed ---> $collectDebtSummary->contract_code]";
			throw new Exception($msg);
		}

		$mailContent = app(DebtRecoveryContractEventBuildContentImproveAction::class)->__buildMailContent($collectDebtEvent);
		$collectDebtEvent->content = $mailContent;
		$collectDebtEvent->status = CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL;
		$collectDebtEvent->time_updated = time();
		
		if (!$collectDebtEvent->save()) {
			throw new Exception('Lỗi không update được nội dung mail');
		}


		$collectDebtSummary->is_send_mail_overdue = 0;
		$r = $collectDebtSummary->save();

		throw_if(!$r, new Exception('Loi cap nhat summary ve trang thai da tao event'));

		return $collectDebtEvent->id;
	}
} // End class
