<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtEvent;

use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use App\Modules\CollectDebt\Model\CollectDebtReminder;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtEvent\SubAction\ChamKyChannelEmailSA;
use App\Modules\CollectDebt\Actions\CollectDebtEvent\SubAction\QuaHanChannelEmailSA;
use App\Modules\CollectDebt\Actions\CollectDebtEvent\SubAction\SapDenKyChannelEmailSA;
use App\Modules\CollectDebt\Actions\CollectDebtEvent\SubAction\SapDenHanChannelEmailSA;

class GenEventContentAction extends BatchProcessingAction
{
	public $timeout = 90;

	public function __construct()
	{
		ini_set('max_execution_time', $this->timeout);
	}

	public function run()
	{
		$results = CollectDebtReminder::query()
																	->where('fired_at', '<=', now())
																	->where('status_create_event', CollectDebtReminder::STT_CHUA_TAO_EVENT)
																	->pluck('id')
																	->toArray();
		if (empty($results)) {
			return ['msg' => 'No data for GenEventContentAction'];
		}

		$ranges = array_chunk($results, $this->batchSize);

		$this->processBatch($ranges);

		return ['msg' => 'ok done'];
	}

	private function processBatch($ranges)
	{
		$baseUrl = config('app.url');

		// Generator
		$requests = function () use ($ranges, $baseUrl) {
			foreach ($ranges as $r) {
				$url = sprintf('%s/ProcessGenContent?ids=%s', $baseUrl, implode(',', $r));
				yield $r => new Request('GET', $url, []);
			}
		};

		$client = $this->createHttpClient();

		$pool = new Pool($client, $requests(), [
			'concurrency' => $this->batchCount,
			'fulfilled' => function ($response, $r) {
				// $body = (string)$response->getBody();
			},
			'rejected' => function (\Throwable $reason, $r) {
				$msg = "[ProcessGenContent ---> failed: " . $reason->getMessage();
				Log::info($msg);
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);

		return true;
	}

	public function ProcessGenContent()
	{
		$ids = request()->get('ids');
		$listReminderIds = array_map('intval', explode(',', $ids));

		$listReminder = CollectDebtReminder::query()->with(['collectDebtGuide.collectDebtSummary', 'collectDebtGuide.collectDebtShare'])->whereIn('id', $listReminderIds)->get();

		if ($listReminder->isEmpty()) {
			return 'No Data';
		}

		foreach ($listReminder as $reminder) {
			try {
				$this->HandleReminder($reminder);
			}catch(\Throwable $th) {

			}
		}

		return true;
	}

	private function canFinishReminderNow(CollectDebtReminder $collectDebtReminder): bool {
		$now = now();
		return (
			$now->gt($collectDebtReminder->fired_at) && !$now->isSameDay($collectDebtReminder->fired_at)
		) || (
			$collectDebtReminder->type == 'SAP_DEN_KY' && $collectDebtReminder->plan_id == 0
		);
	}

	
	public function HandleReminder(CollectDebtReminder $collectDebtReminder)
	{
		// Reminder quá khứ thì không xử lý
		if ( $this->canFinishReminderNow($collectDebtReminder) ) {
			$collectDebtReminder->status_create_event = CollectDebtReminder::STT_DA_TAO_EVENT;
			$collectDebtReminder->create_event_at = now();
			$collectDebtReminder->description = 'Reminder quá khứ --> không xử lý';
			$collectDebtReminder->save();
			return $collectDebtReminder->id;
		}

		$contractCode = $collectDebtReminder->contract_code;
		$collectDebtGuide = $collectDebtReminder->collectDebtGuide;

		// Kết thúc reminder nếu guide ko tồn tại hoặc HĐ đã tất toán
		if (!$collectDebtGuide || $collectDebtGuide->collectDebtSummary->isHopDongDaTatToan()) {
			$collectDebtReminder->status_create_event = CollectDebtReminder::STT_DA_TAO_EVENT;
			$collectDebtReminder->create_event_at = now();
			$collectDebtReminder->description = 'Không có guide hoặc HĐ đã tất toán';
			$collectDebtReminder->save();
			return $collectDebtReminder->id;
		}

		// Xử lý `sắp đến hạn` & `đến hạn`
		if ($collectDebtReminder->type == 'SAP_DEN_HAN' && $collectDebtReminder->channel == 'MAIL') {
			return app(SapDenHanChannelEmailSA::class)->run(
				$collectDebtReminder,
				$collectDebtGuide
			);
		}

		// Xử lý `sắp đến kỳ` & `đến kỳ`
		if ($collectDebtReminder->type == 'SAP_DEN_KY' && $collectDebtReminder->channel == 'MAIL') {
			return app(SapDenKyChannelEmailSA::class)->run($collectDebtReminder);
		}

		// Xử lý `chậm kỳ`
		if ($collectDebtReminder->type == 'CHAM_KY' && $collectDebtReminder->channel == 'MAIL') {
			return app(ChamKyChannelEmailSA::class)->run($collectDebtReminder);
		}

		// Xử lý `quá hạn`
		if ($collectDebtReminder->type == 'QUA_HAN' && $collectDebtReminder->channel == 'MAIL') {
			return app(QuaHanChannelEmailSA::class)->run($collectDebtReminder);
		}

		return $contractCode;
	}
}  // End class